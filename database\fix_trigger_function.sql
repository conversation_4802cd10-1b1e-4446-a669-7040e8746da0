-- Migration to fix the calculate_profit_loss trigger function
-- This removes any references to removed fields like stop_price, direction, shares, etc.
-- Run this in your Supabase SQL Editor

-- Step 1: Drop the existing trigger
DROP TRIGGER IF EXISTS calculate_profit_loss_trigger ON trades;

-- Step 2: Drop any old constraints that might reference removed fields
ALTER TABLE trades DROP CONSTRAINT IF EXISTS valid_stock_trade;

-- Step 3: Update the trigger function to only handle option trades
CREATE OR REPLACE FUNCTION calculate_profit_loss()
RETURNS TRIGGER AS $$
BEGIN
    -- Only handle option trades (since we removed stock trading support)
    -- Calculate fees: $1.30 per contract for options
    NEW.fees = NEW.contracts * 1.30;

    -- Calculate profit/loss when both entry and exit prices are available
    IF NEW.exit_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        -- Options: multiply by 100 for contract multiplier, subtract fees
        NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.contracts * 100 - NEW.fees;
    END IF;

    -- Update the updated_at timestamp
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Step 4: Recreate the trigger
CREATE TRIGGER calculate_profit_loss_trigger
    BEFORE INSERT OR UPDATE ON trades
    FOR EACH ROW
    EXECUTE FUNCTION calculate_profit_loss();

-- Step 5: Verify the function was updated correctly
SELECT routine_name, routine_definition 
FROM information_schema.routines 
WHERE routine_name = 'calculate_profit_loss' 
AND routine_schema = 'public';
