-- Migration to fix percentage_return calculation for existing vertical spread trades
-- This recalculates percentage_return based on risk amount instead of entry price
-- Run this in your Supabase SQL Editor

-- Step 1: Show current vertical spread trades with their percentage_return values (for verification)
SELECT 
    id,
    ticker,
    trade_type,
    spread_entry_type,
    contracts,
    lower_strike,
    upper_strike,
    entry_price,
    exit_price,
    profit_loss,
    percentage_return as old_percentage_return,
    risk_amount,
    entry_datetime
FROM trades 
WHERE trade_type = 'vertical' 
    AND exit_price IS NOT NULL 
    AND profit_loss IS NOT NULL
    AND risk_amount IS NOT NULL
    AND risk_amount > 0
ORDER BY entry_datetime DESC;

-- Step 2: Update percentage_return for existing vertical spread trades
-- New calculation: percentage_return = (profit_loss / risk_amount) * 100
UPDATE trades 
SET 
    percentage_return = CASE 
        WHEN risk_amount IS NOT NULL AND risk_amount > 0 AND profit_loss IS NOT NULL THEN
            ROUND(((profit_loss / risk_amount) * 100)::numeric, 4)
        ELSE 
            NULL
    END,
    updated_at = NOW()
WHERE trade_type = 'vertical' 
    AND exit_price IS NOT NULL 
    AND profit_loss IS NOT NULL
    AND risk_amount IS NOT NULL
    AND risk_amount > 0;

-- Step 3: Show updated vertical spread trades (for verification)
SELECT 
    id,
    ticker,
    trade_type,
    spread_entry_type,
    contracts,
    lower_strike,
    upper_strike,
    entry_price,
    exit_price,
    profit_loss,
    percentage_return as new_percentage_return,
    risk_amount,
    entry_datetime
FROM trades 
WHERE trade_type = 'vertical' 
    AND exit_price IS NOT NULL 
    AND profit_loss IS NOT NULL
    AND risk_amount IS NOT NULL
    AND risk_amount > 0
ORDER BY entry_datetime DESC;

-- Step 4: Show summary of changes
SELECT 
    'Vertical Spread Percentage Returns Fixed' as status,
    COUNT(*) as total_vertical_trades_updated,
    AVG(percentage_return) as avg_new_percentage_return,
    MIN(percentage_return) as min_percentage_return,
    MAX(percentage_return) as max_percentage_return
FROM trades 
WHERE trade_type = 'vertical' 
    AND exit_price IS NOT NULL 
    AND profit_loss IS NOT NULL
    AND risk_amount IS NOT NULL
    AND risk_amount > 0;

-- Step 5: Handle any vertical spreads that might not have risk_amount calculated
-- (This should recalculate risk_amount for any vertical spreads missing it)
UPDATE trades 
SET 
    risk_amount = CASE 
        WHEN trade_type = 'vertical' AND contracts IS NOT NULL AND lower_strike IS NOT NULL 
             AND upper_strike IS NOT NULL AND spread_entry_type IS NOT NULL THEN
            CASE 
                WHEN spread_entry_type = 'debit' THEN
                    ROUND((entry_price * 100 * contracts)::numeric, 2)
                ELSE -- credit
                    ROUND(((ABS(upper_strike - lower_strike) - entry_price) * 100 * contracts)::numeric, 2)
            END
        ELSE 
            risk_amount
    END,
    updated_at = NOW()
WHERE trade_type = 'vertical' 
    AND (risk_amount IS NULL OR risk_amount = 0)
    AND contracts IS NOT NULL 
    AND lower_strike IS NOT NULL 
    AND upper_strike IS NOT NULL 
    AND spread_entry_type IS NOT NULL;

-- Step 6: Now update percentage_return for any vertical spreads that were missing risk_amount
UPDATE trades 
SET 
    percentage_return = CASE 
        WHEN risk_amount IS NOT NULL AND risk_amount > 0 AND profit_loss IS NOT NULL THEN
            ROUND(((profit_loss / risk_amount) * 100)::numeric, 4)
        ELSE 
            NULL
    END,
    updated_at = NOW()
WHERE trade_type = 'vertical' 
    AND exit_price IS NOT NULL 
    AND profit_loss IS NOT NULL
    AND risk_amount IS NOT NULL
    AND risk_amount > 0
    AND percentage_return IS NULL;

-- Step 7: Final verification - show all vertical spread trades
SELECT 
    'Final Results' as section,
    COUNT(*) as total_vertical_trades,
    COUNT(CASE WHEN percentage_return IS NOT NULL THEN 1 END) as trades_with_percentage_return,
    COUNT(CASE WHEN risk_amount IS NOT NULL AND risk_amount > 0 THEN 1 END) as trades_with_risk_amount
FROM trades 
WHERE trade_type = 'vertical';
