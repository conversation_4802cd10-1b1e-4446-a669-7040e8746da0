-- Migration to add Maximum Adverse Excursion (MAE) field to trades table
-- This migration adds the mae_price column for option trades

-- Add the MAE price column to the trades table
ALTER TABLE trades 
ADD COLUMN IF NOT EXISTS mae_price DECIMAL(10,2);

-- Add comment to document the new column
COMMENT ON COLUMN trades.mae_price IS 'Maximum Adverse Excursion price for option trades - the worst price reached during the trade';

-- Update any existing trades to have NULL mae_price (which is the default)
-- No data update needed since new column defaults to NULL

-- Verify the column was added successfully
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'trades' AND column_name = 'mae_price';

