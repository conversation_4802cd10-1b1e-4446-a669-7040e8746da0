-- Migration: Add new fields to trades table for enhanced trade tracking
-- Run this in your Supabase SQL Editor

-- Step 1: Add new columns to trades table
ALTER TABLE trades 
ADD COLUMN IF NOT EXISTS trade_setup VARCHAR(100),
ADD COLUMN IF NOT EXISTS support_type VARCHAR(100),
ADD COLUMN IF NOT EXISTS price_to_vwap_position VARCHAR(10) CHECK (price_to_vwap_position IN ('Above', 'At', 'Below'));

-- Step 2: Add comments to document the new columns
COMMENT ON COLUMN trades.trade_setup IS 'User-defined trade setup strategy (references trade_setups table)';
COMMENT ON COLUMN trades.support_type IS 'User-defined support type (references support_types table)';
COMMENT ON COLUMN trades.price_to_vwap_position IS 'Position relative to VWAP: Above, At, or Below';

-- Step 3: Create trade_setups table for user-customizable dropdown options
CREATE TABLE IF NOT EXISTS trade_setups (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Step 4: Create support_types table for user-customizable dropdown options
CREATE TABLE IF NOT EXISTS support_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Step 5: Enable Row Level Security (RLS) for the new tables
ALTER TABLE trade_setups ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_types ENABLE ROW LEVEL SECURITY;

-- Step 6: Create RLS policies for trade_setups
CREATE POLICY "Users can view their own trade setups" ON trade_setups
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trade setups" ON trade_setups
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trade setups" ON trade_setups
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trade setups" ON trade_setups
    FOR DELETE USING (auth.uid() = user_id);

-- Step 7: Create RLS policies for support_types
CREATE POLICY "Users can view their own support types" ON support_types
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own support types" ON support_types
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own support types" ON support_types
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own support types" ON support_types
    FOR DELETE USING (auth.uid() = user_id);

-- Step 8: Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trade_setups_user_id ON trade_setups(user_id);
CREATE INDEX IF NOT EXISTS idx_support_types_user_id ON support_types(user_id);

-- Step 9: Insert some default trade setups and support types for existing users (optional)
-- Note: This will only work if there are existing users in the system
-- You can customize these default values as needed

-- Verify the migration was successful
SELECT column_name, data_type, is_nullable, column_default
FROM information_schema.columns 
WHERE table_name = 'trades' AND column_name IN ('trade_setup', 'support_type', 'price_to_vwap_position')
ORDER BY column_name;

-- Verify the new tables were created
SELECT table_name 
FROM information_schema.tables 
WHERE table_name IN ('trade_setups', 'support_types') 
AND table_schema = 'public';
