-- Migration to remove stock and vertical spread trade columns
-- This migration removes columns that are no longer needed after simplifying to option trades only

-- Drop stock-specific columns
ALTER TABLE trades DROP COLUMN IF EXISTS direction;
ALTER TABLE trades DROP COLUMN IF EXISTS shares;
ALTER TABLE trades DROP COLUMN IF EXISTS stop_price;
ALTER TABLE trades DROP COLUMN IF EXISTS r_return;
ALTER TABLE trades DROP COLUMN IF EXISTS risk_amount;

-- Drop vertical spread-specific columns
ALTER TABLE trades DROP COLUMN IF EXISTS spread_type;
ALTER TABLE trades DROP COLUMN IF EXISTS lower_strike;
ALTER TABLE trades DROP COLUMN IF EXISTS upper_strike;
ALTER TABLE trades DROP COLUMN IF EXISTS spread_entry_type;

-- Verify the columns were dropped successfully
SELECT column_name, data_type
FROM information_schema.columns 
WHERE table_name = 'trades'
ORDER BY ordinal_position;

