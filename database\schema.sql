-- Trading Journal Database Schema
-- Run this in your Supabase SQL Editor

-- Create trades table
CREATE TABLE IF NOT EXISTS trades (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    status VARCHAR(6) NOT NULL DEFAULT 'Open' CHECK (status IN ('Open', 'Closed')),
    entry_datetime TIMESTAMPTZ NOT NULL,
    exit_datetime TIMESTAMPTZ,
    ticker VARCHAR(10) NOT NULL,
    trade_type VARCHAR(6) NOT NULL DEFAULT 'option' CHECK (trade_type = 'option'),
    -- Option fields
    option_type VARCHAR(4) NOT NULL CHECK (option_type IN ('Call', 'Put')),
    option_strike DECIMAL(10,2) NOT NULL,
    option_expiration DATE NOT NULL,
    contracts INTEGER NOT NULL CHECK (contracts > 0),
    -- Price tracking
    mae_price DECIMAL(10,2), -- Maximum Adverse Excursion price - worst price during the trade
    mfe_price DECIMAL(10,2), -- Maximum Favorable Excursion price - best price during the trade
    -- Trade analysis fields
    trade_setup VARCHAR(100), -- User-defined trade setup strategy
    support_type VARCHAR(100), -- User-defined support type
    price_to_vwap_position VARCHAR(10) CHECK (price_to_vwap_position IN ('Above', 'At', 'Below')), -- Position relative to VWAP
    -- Common fields
    entry_price DECIMAL(10,2) NOT NULL,
    exit_price DECIMAL(10,2),
    fees DECIMAL(10,2),
    profit_loss DECIMAL(10,2),
    percentage_return DECIMAL(8,4), -- Percentage return (e.g., 15.25 for 15.25%)
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create function to automatically calculate fees and profit/loss for option trades
CREATE OR REPLACE FUNCTION calculate_profit_loss()
RETURNS TRIGGER AS $$
BEGIN
    -- Calculate fees: $1.30 per contract
    NEW.fees = NEW.contracts * 1.30;

    -- Calculate profit/loss when both entry and exit prices are available
    IF NEW.exit_price IS NOT NULL AND NEW.entry_price IS NOT NULL THEN
        -- Options: multiply by 100 for contract multiplier
        NEW.profit_loss = (NEW.exit_price - NEW.entry_price) * NEW.contracts * 100 - NEW.fees;
    END IF;

    -- Update the updated_at timestamp
    NEW.updated_at = NOW();

    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically calculate profit/loss
DROP TRIGGER IF EXISTS calculate_profit_loss_trigger ON trades;
CREATE TRIGGER calculate_profit_loss_trigger
    BEFORE INSERT OR UPDATE ON trades
    FOR EACH ROW
    EXECUTE FUNCTION calculate_profit_loss();

-- Create index for faster queries
CREATE INDEX IF NOT EXISTS idx_trades_entry_datetime ON trades(entry_datetime);
CREATE INDEX IF NOT EXISTS idx_trades_ticker ON trades(ticker);
CREATE INDEX IF NOT EXISTS idx_trades_created_at ON trades(created_at);

-- Create view for daily statistics
CREATE OR REPLACE VIEW daily_stats AS
SELECT 
    DATE(entry_datetime) as trade_date,
    COUNT(*) as trade_count,
    SUM(CASE WHEN profit_loss > 0 THEN 1 ELSE 0 END) as winning_trades,
    SUM(CASE WHEN profit_loss < 0 THEN 1 ELSE 0 END) as losing_trades,
    COALESCE(SUM(profit_loss), 0) as total_profit_loss,
    COALESCE(AVG(profit_loss), 0) as avg_profit_loss
FROM trades 
WHERE exit_price IS NOT NULL
GROUP BY DATE(entry_datetime)
ORDER BY trade_date DESC;

-- Enable Row Level Security (RLS)
ALTER TABLE trades ENABLE ROW LEVEL SECURITY;

-- Create policies for user-specific access
CREATE POLICY "Users can view their own trades" ON trades
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trades" ON trades
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trades" ON trades
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trades" ON trades
    FOR DELETE USING (auth.uid() = user_id);

-- Create user preferences table
CREATE TABLE IF NOT EXISTS user_preferences (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL UNIQUE,
    theme VARCHAR(10) CHECK (theme IN ('light', 'dark')) DEFAULT 'light',
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create trade_setups table for user-customizable dropdown options
CREATE TABLE IF NOT EXISTS trade_setups (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Create support_types table for user-customizable dropdown options
CREATE TABLE IF NOT EXISTS support_types (
    id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
    user_id UUID REFERENCES auth.users(id) ON DELETE CASCADE NOT NULL,
    name VARCHAR(100) NOT NULL,
    created_at TIMESTAMPTZ DEFAULT NOW(),
    updated_at TIMESTAMPTZ DEFAULT NOW(),
    UNIQUE(user_id, name)
);

-- Enable Row Level Security (RLS) for user_preferences
ALTER TABLE user_preferences ENABLE ROW LEVEL SECURITY;

-- Create policies for user-specific access to preferences
CREATE POLICY "Users can view their own preferences" ON user_preferences
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own preferences" ON user_preferences
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own preferences" ON user_preferences
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own preferences" ON user_preferences
    FOR DELETE USING (auth.uid() = user_id);

-- Enable Row Level Security (RLS) for trade_setups and support_types
ALTER TABLE trade_setups ENABLE ROW LEVEL SECURITY;
ALTER TABLE support_types ENABLE ROW LEVEL SECURITY;

-- Create policies for trade_setups
CREATE POLICY "Users can view their own trade setups" ON trade_setups
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own trade setups" ON trade_setups
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own trade setups" ON trade_setups
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own trade setups" ON trade_setups
    FOR DELETE USING (auth.uid() = user_id);

-- Create policies for support_types
CREATE POLICY "Users can view their own support types" ON support_types
    FOR SELECT USING (auth.uid() = user_id);

CREATE POLICY "Users can insert their own support types" ON support_types
    FOR INSERT WITH CHECK (auth.uid() = user_id);

CREATE POLICY "Users can update their own support types" ON support_types
    FOR UPDATE USING (auth.uid() = user_id);

CREATE POLICY "Users can delete their own support types" ON support_types
    FOR DELETE USING (auth.uid() = user_id);

-- Create function to automatically update updated_at timestamp
CREATE OR REPLACE FUNCTION update_user_preferences_updated_at()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ LANGUAGE plpgsql;

-- Create trigger to automatically update updated_at timestamp
DROP TRIGGER IF EXISTS update_user_preferences_updated_at_trigger ON user_preferences;
CREATE TRIGGER update_user_preferences_updated_at_trigger
    BEFORE UPDATE ON user_preferences
    FOR EACH ROW
    EXECUTE FUNCTION update_user_preferences_updated_at();

-- Create indexes for better performance
CREATE INDEX IF NOT EXISTS idx_trades_user_id ON trades(user_id);
CREATE INDEX IF NOT EXISTS idx_trades_entry_datetime ON trades(entry_datetime);
CREATE INDEX IF NOT EXISTS idx_trades_ticker ON trades(ticker);
CREATE INDEX IF NOT EXISTS idx_trades_status ON trades(status);
CREATE INDEX IF NOT EXISTS idx_user_preferences_user_id ON user_preferences(user_id);
CREATE INDEX IF NOT EXISTS idx_trade_setups_user_id ON trade_setups(user_id);
CREATE INDEX IF NOT EXISTS idx_support_types_user_id ON support_types(user_id);

-- Note: Sample data removed due to user authentication requirements
-- Users will need to add their own trades after signing up
