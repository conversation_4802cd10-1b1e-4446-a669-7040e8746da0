import { NextRequest, NextResponse } from 'next/server'

// Server-only: expects ALPACA_KEY_ID and ALPACA_SECRET_KEY in env
const ALPACA_BASE_URL = 'https://data.alpaca.markets/v2'

function toRFC3339(dateStr: string) {
  // Ensure RFC3339 with Z
  const d = new Date(dateStr)
  return d.toISOString()
}

export async function GET(req: NextRequest) {
  const { searchParams } = new URL(req.url)
  const symbol = (searchParams.get('symbol') || '').toUpperCase()
  const start = searchParams.get('start')
  const end = searchParams.get('end')
  const date = searchParams.get('date') // YYYY-MM-DD (ET)
  const timeframeParam = (searchParams.get('timeframe') || '5m').toLowerCase()

  const allowedTimeframes: Record<string, string> = {
    '1m': '1Min',
    '5m': '5Min',
    '15m': '15Min',
  }

  const alpacaTimeframe = allowedTimeframes[timeframeParam]
  if (!alpacaTimeframe) {
    return NextResponse.json({ error: 'Invalid timeframe. Use 1m, 5m, or 15m.' }, { status: 400 })
  }

  if (!symbol) {
    return NextResponse.json({ error: 'symbol is required' }, { status: 400 })
  }

  const keyId = process.env.ALPACA_KEY_ID
  const secret = process.env.ALPACA_SECRET_KEY
  if (!keyId || !secret) {
    return NextResponse.json({ error: 'Alpaca API keys not configured' }, { status: 500 })
  }

  // Resolve start/end when 'date' is provided (pull full day)
  let resolvedStart: string | null = null
  let resolvedEnd: string | null = null
  if (date) {
    // Date is in ET. Fetch a generous UTC range: [date 00:00:00 ET, next day 00:00:00 ET)
    // Compute those in UTC by constructing Date from the ET local midnight via Intl workaround
    const startUtc = new Date(`${date}T00:00:00-04:00`) // This is a best-effort; we will still filter to ET day below
    const endUtc = new Date(new Date(startUtc.getTime() + 24 * 60 * 60 * 1000).toISOString())
    resolvedStart = startUtc.toISOString()
    resolvedEnd = endUtc.toISOString()
  } else {
    if (!start || !end) {
      return NextResponse.json({ error: 'Provide either date or start/end' }, { status: 400 })
    }
    resolvedStart = toRFC3339(start)
    resolvedEnd = toRFC3339(end)
  }

  // Build Alpaca bars request: 5Min timeframe
  const params = new URLSearchParams({
    timeframe: alpacaTimeframe,
    start: resolvedStart!,
    end: resolvedEnd!,
    limit: '10000',
    adjustment: 'raw',
  })

  const url = `${ALPACA_BASE_URL}/stocks/${encodeURIComponent(symbol)}/bars?${params.toString()}`

  try {
    const res = await fetch(url, {
      headers: {
        'APCA-API-KEY-ID': keyId,
        'APCA-API-SECRET-KEY': secret,
      },
      // Cache a bit to cut API usage while navigating
      next: { revalidate: 60 },
    })

    if (!res.ok) {
      const text = await res.text()
      return NextResponse.json({ error: text || 'Failed to fetch Alpaca data' }, { status: res.status })
    }

    const json = await res.json()
    // Alpaca returns { bars: [ { t, o, h, l, c, ... } ], symbol }
    type AlpacaBar = { t: string; o: number; h: number; l: number; c: number }
    let candles: { time: number; open: number; high: number; low: number; close: number }[] = (json?.bars || []).map((b: AlpacaBar) => ({
      time: Math.floor(new Date(b.t).getTime() / 1000),
      open: Number(b.o),
      high: Number(b.h),
      low: Number(b.l),
      close: Number(b.c),
    }))

    // If 'date' provided: filter to ET date and RTH (9:30 ET to < 16:00 ET)
    if (date) {
      const et = new Intl.DateTimeFormat('en-US', { timeZone: 'America/New_York', hour: 'numeric', minute: 'numeric', hour12: false, year: 'numeric', month: '2-digit', day: '2-digit' })
      const etDateOnly = new Intl.DateTimeFormat('en-CA', { timeZone: 'America/New_York', year: 'numeric', month: '2-digit', day: '2-digit' })

      candles = candles.filter((c: { time: number }) => {
        const dt = new Date(c.time * 1000)
        const dstr = etDateOnly.format(dt) // YYYY-MM-DD in ET
        if (dstr !== date) return false
        const parts = et.formatToParts(dt)
        const hh = Number(parts.find(p => p.type === 'hour')?.value || '0')
        const mm = Number(parts.find(p => p.type === 'minute')?.value || '0')
        const minutes = hh * 60 + mm
        // Keep bars starting at >= 9:30 (570) and < 16:00 (960)
        return minutes >= 570 && minutes < 960
      })
    }

    return NextResponse.json({ candles })
  } catch (err: unknown) {
    const message = err instanceof Error ? err.message : 'Unknown error'
    return NextResponse.json({ error: message }, { status: 500 })
  }
}
