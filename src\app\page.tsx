'use client'

import { useState, useEffect } from 'react'
import Calendar from '@/components/Calendar'
import AddTradeModal from '@/components/AddTradeModal'
import EditTradeModal from '@/components/EditTradeModal'
import TradeLog from '@/components/TradeLog'
import TradeDetailsModal from '@/components/TradeDetailsModal'
import AuthModal from '@/components/AuthModal'
import UserMenu from '@/components/UserMenu'
import ThemeToggle from '@/components/ThemeToggle'
import { isSupabaseConfigured, Trade } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'
import { useTheme } from '@/contexts/ThemeContext'

export default function Home() {
  const { user, loading } = useAuth()
  const { theme, loadUserTheme, saveThemeToDatabase } = useTheme()
  const [isAddTradeModalOpen, setIsAddTradeModalOpen] = useState(false)
  const [isEditTradeModalOpen, setIsEditTradeModalOpen] = useState(false)
  const [isDetailsModalOpen, setIsDetailsModalOpen] = useState(false)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const [editingTrade, setEditingTrade] = useState<Trade | null>(null)
  const [viewingTrade, setViewingTrade] = useState<Trade | null>(null)
  const [refreshTrigger, setRefreshTrigger] = useState(0)
  const [selectedDate, setSelectedDate] = useState<string | undefined>()
  const [activeTab, setActiveTab] = useState<'calendar' | 'log'>('calendar')
  const [prevUser, setPrevUser] = useState(user)
  const [pendingEditAction, setPendingEditAction] = useState<{ previousTrade: Trade; updates: Partial<Trade> } | null>(null)

  // Detect user sign in/out and refresh data
  useEffect(() => {
    if (prevUser !== user) {
      // User state changed (sign in or sign out) - refresh all data
      setRefreshTrigger(prev => prev + 1)

      if (prevUser && !user) {
        // User signed out - also clear any date filters
        setSelectedDate(undefined)
      } else if (!prevUser && user) {
        // User signed in - load their theme preference
        loadUserTheme()
      }
    }
    setPrevUser(user)
  }, [user, prevUser, loadUserTheme])

  // Save theme to database when it changes (for authenticated users)
  useEffect(() => {
    if (user && theme) {
      saveThemeToDatabase(theme)
    }
  }, [user, theme, saveThemeToDatabase])

  const handleTradeAdded = () => {
    setRefreshTrigger(prev => prev + 1)
    setIsAddTradeModalOpen(false)
  }

  const handleTradeUpdated = ({ previousTrade, updates }: { previousTrade: Trade; updates: Partial<Trade> }) => {
    setPendingEditAction({ previousTrade, updates })
    setIsEditTradeModalOpen(false)
    setEditingTrade(null)
  }

  const handleEditTrade = (trade: Trade) => {
    setEditingTrade(trade)
    setIsEditTradeModalOpen(true)
  }

  const handleViewTrade = (trade: Trade) => {
    setViewingTrade(trade)
    setIsDetailsModalOpen(true)
  }

  const handleDateClick = (date: string) => {
    setSelectedDate(date)
    setActiveTab('log')
  }

  const clearDateFilter = () => {
    setSelectedDate(undefined)
  }

  const handleAddTradeClick = () => {
    if (isSupabaseConfigured && !user) {
      setIsAuthModalOpen(true)
    } else {
      setIsAddTradeModalOpen(true)
    }
  }

  // Show loading spinner while checking authentication
  if (loading) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-gray-500 dark:text-gray-400">Loading...</div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <header className="bg-white dark:bg-gray-800 shadow-sm border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col space-y-4 py-4 sm:flex-row sm:justify-between sm:items-center sm:space-y-0 sm:py-6">
            {/* Title Section */}
            <div className="text-center sm:text-left">
              <h1 className="text-xl sm:text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white">Trading Journal</h1>
              <p className="text-sm sm:text-base text-gray-600 dark:text-gray-300 mt-1">Track your trades and analyze your performance</p>
            </div>

            {/* Controls Section */}
            <div className="flex flex-col space-y-3 sm:flex-row sm:items-center sm:space-y-0 sm:space-x-4">
              {/* Tab Navigation */}
              <div className="flex space-x-1 bg-gray-100 dark:bg-gray-700 rounded-lg p-1 mx-auto sm:mx-0">
                <button
                  type="button"
                  onClick={() => setActiveTab('calendar')}
                  className={`px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors ${
                    activeTab === 'calendar'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Calendar
                </button>
                <button
                  type="button"
                  onClick={() => setActiveTab('log')}
                  className={`px-3 py-2 rounded-md text-xs sm:text-sm font-medium transition-colors ${
                    activeTab === 'log'
                      ? 'bg-white dark:bg-gray-600 text-gray-900 dark:text-white shadow-sm'
                      : 'text-gray-600 dark:text-gray-300 hover:text-gray-900 dark:hover:text-white'
                  }`}
                >
                  Trade Log
                </button>
              </div>

              {/* Action Buttons */}
              <div className="flex items-center justify-center space-x-2 sm:space-x-3 ml-2.5">
                
                {/* Add Trade Button */}
                <button
                  type="button"
                  onClick={handleAddTradeClick}
                  className="flex items-center space-x-1 sm:space-x-2 bg-blue-600 dark:bg-blue-500 text-white px-3 py-2 rounded-lg hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors text-sm"
                >
                  <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 4v16m8-8H4" />
                  </svg>
                  <span className="hidden xs:inline sm:inline">Add Trade</span>
                  <span className="xs:hidden sm:hidden">Add</span>
                </button>

                {/* Theme Toggle */}
                <ThemeToggle />

                {/* Authentication UI */}
                {user ? (
                  <UserMenu />
                ) : (
                  <button
                    type="button"
                    onClick={() => setIsAuthModalOpen(true)}
                    className="bg-blue-600 dark:bg-blue-500 text-white px-3 py-2 rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 transition-colors text-sm"
                  >
                    <span className="hidden xs:inline">Login</span>
                    <span className="xs:hidden">Login</span>
                  </button>
                )}
              </div>
            </div>
          </div>
        </div>
      </header>

      {/* Configuration Notice */}
      {!isSupabaseConfigured && (
        <div className="bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg p-4 mx-4 sm:mx-6 lg:mx-8 mt-4">
          <div className="flex">
            <div className="ml-3">
              <h3 className="text-sm font-medium text-yellow-800 dark:text-yellow-200">
                Demo Mode - Supabase Not Configured
              </h3>
              <div className="mt-2 text-sm text-yellow-700 dark:text-yellow-300">
                <p>
                  You are viewing the app in demo mode with sample data. To use with real data,
                  please configure your Supabase credentials in the .env.local file and set up the database schema.
                  See the README.md for setup instructions.
                </p>
              </div>
            </div>
          </div>
        </div>
      )}

      {/* Main Content */}
      <main className="w-full px-2 sm:px-4 lg:px-6 py-4 sm:py-6 lg:py-8">
        {activeTab === 'calendar' ? (
          <Calendar
            onDateClick={handleDateClick}
            refreshTrigger={refreshTrigger}
          />
        ) : (
          <div className="space-y-4">
            {selectedDate && (
              <div className="flex items-center justify-between bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4">
                <span className="text-blue-800 dark:text-blue-200">
                  Showing trades for {new Date(selectedDate).toLocaleDateString()}
                </span>
                <button
                  type="button"
                  onClick={clearDateFilter}
                  className="text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 text-sm font-medium"
                >
                  Show all trades
                </button>
              </div>
            )}
            <TradeLog
              refreshTrigger={refreshTrigger}
              selectedDate={selectedDate}
              onEditTrade={handleEditTrade}
              onViewTrade={handleViewTrade}
              pendingEditAction={pendingEditAction}
              onPendingEditHandled={() => setPendingEditAction(null)}
              onRefreshRequested={() => setRefreshTrigger(prev => prev + 1)}
            />
          </div>
        )}
      </main>

      {/* Add Trade Modal */}
      <AddTradeModal
        isOpen={isAddTradeModalOpen}
        onClose={() => setIsAddTradeModalOpen(false)}
        onTradeAdded={handleTradeAdded}
      />

      {/* Edit Trade Modal */}
      <EditTradeModal
        isOpen={isEditTradeModalOpen}
        onClose={() => setIsEditTradeModalOpen(false)}
        onTradeUpdated={handleTradeUpdated}
        trade={editingTrade}
      />

      {/* Trade Details Modal */}
      <TradeDetailsModal
        isOpen={isDetailsModalOpen}
        onClose={() => setIsDetailsModalOpen(false)}
        trade={viewingTrade}
        onEditTrade={handleEditTrade}
      />

      {/* Auth Modal */}
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
      />
    </div>
  )
}
