﻿'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { X } from 'lucide-react'
import { createTrade, getTradeSetups, getSupportTypes } from '@/lib/database'
import { TradeSetup, SupportType } from '@/lib/supabase'
import { normalizeDateTimeLocal } from '@/lib/datetime'
import { format } from 'date-fns'
import OptionTradeForm, { optionTradeSchema, OptionTradeFormData } from './OptionTradeForm'

interface AddTradeModalProps {
  isOpen: boolean
  onClose: () => void
  onTradeAdded: () => void
}

export default function AddTradeModal({ isOpen, onClose, onTradeAdded }: AddTradeModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [tradeSetups, setTradeSetups] = useState<TradeSetup[]>([])
  const [supportTypes, setSupportTypes] = useState<SupportType[]>([])

  // Load dropdown data when modal opens
  useEffect(() => {
    if (isOpen) {
      loadDropdownData()
    }
  }, [isOpen])

  const loadDropdownData = async () => {
    try {
      const [setupsData, typesData] = await Promise.all([
        getTradeSetups(),
        getSupportTypes()
      ])
      setTradeSetups(setupsData)
      setSupportTypes(typesData)
    } catch (error) {
      console.error('Error loading dropdown data:', error)
    }
  }

  // Option trade form
  const optionForm = useForm<OptionTradeFormData>({
    resolver: zodResolver(optionTradeSchema),
    defaultValues: {
      status: 'Open',
      contracts: 1,
      entry_datetime: format(new Date(), "yyyy-MM-dd'T'HH:mm"),
      option_expiration: format(new Date(), "yyyy-MM-dd"),
    }
  })


  // Preserve datetime-local values as entered
  const preserveLocalDateTime = (datetimeLocal: string | null) => normalizeDateTimeLocal(datetimeLocal) || ''

  const onSubmit = async (data: OptionTradeFormData) => {
    try {
      setIsSubmitting(true)

      const tradeData = {
        trade_type: 'option' as const,
        status: data.status,
        entry_datetime: preserveLocalDateTime(data.entry_datetime),
        exit_datetime: data.exit_datetime ? normalizeDateTimeLocal(data.exit_datetime) : null,
        ticker: data.ticker,
        option_type: data.option_type,
        option_strike: data.option_strike,
        option_expiration: data.option_expiration,
        contracts: data.contracts,
        mae_price: data.mae_price || null,
        mfe_price: data.mfe_price || null,
        trade_setup: data.trade_setup || null,
        support_type: data.support_type || null,
        price_to_vwap_position: data.price_to_vwap_position || null,
        entry_price: data.entry_price,
        exit_price: data.exit_price || null,
      }

      await createTrade(tradeData)
      optionForm.reset()
      onClose()
      onTradeAdded()
    } catch (error) {
      console.error('Error creating option trade:', error)
      alert('Failed to create option trade. Please check your input and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    optionForm.reset()
    onClose()
  }

  if (!isOpen) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">Add New Trade</h2>
          <button
            type="button"
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
            title="Close modal"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        <div className="p-4 sm:p-6">
          <form onSubmit={optionForm.handleSubmit(onSubmit)} className="space-y-4">
            <OptionTradeForm
              register={optionForm.register}
              watch={optionForm.watch}
              setValue={optionForm.setValue}
              errors={optionForm.formState.errors}
              tradeSetups={tradeSetups}
              supportTypes={supportTypes}
            />

            <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                type="button"
                onClick={handleClose}
                className="w-full sm:w-auto px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full sm:w-auto px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? 'Adding...' : 'Add Trade'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
