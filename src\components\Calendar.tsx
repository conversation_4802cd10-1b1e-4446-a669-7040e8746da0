'use client'

import React, { useState, useEffect, useCallback } from 'react'
import { ChevronLeft, ChevronRight } from 'lucide-react'
import { format, startOfMonth, endOfMonth, isSameMonth, isToday, startOfWeek, endOfWeek } from 'date-fns'
import { getCalendarData } from '@/lib/database'
import { DailyStats, isSupabaseConfigured } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface CalendarProps {
  onDateClick: (date: string) => void
  refreshTrigger: number
}

export default function Calendar({ onDateClick, refreshTrigger }: CalendarProps) {
  const { user } = useAuth()
  const [currentDate, setCurrentDate] = useState(new Date())
  const [dailyStats, setDailyStats] = useState<DailyStats[]>([])
  const [loading, setLoading] = useState(true)
  const [showMonthYearPicker, setShowMonthYearPicker] = useState(false)

  const monthStart = startOfMonth(currentDate)
  const monthEnd = endOfMonth(currentDate)

  // Get the start and end of the calendar grid (including previous/next month days)
  const calendarStart = startOfWeek(monthStart, { weekStartsOn: 0 }) // 0 = Sunday
  const calendarEnd = endOfWeek(monthEnd, { weekStartsOn: 0 })

  // Get all days to display in the calendar grid
  // const days = eachDayOfInterval({ start: calendarStart, end: calendarEnd })

  const loadCalendarData = useCallback(async () => {
    try {
      setLoading(true)

      // If Supabase is configured but user is not signed in, show empty data
      if (isSupabaseConfigured && !user) {
        setDailyStats([])
        return
      }

      const year = currentDate.getFullYear()
      const month = currentDate.getMonth() + 1
      const data = await getCalendarData(year, month)
      setDailyStats(data || [])
    } catch (error) {
      console.error('Error loading calendar data:', error)
      setDailyStats([])
    } finally {
      setLoading(false)
    }
  }, [currentDate, user])

  useEffect(() => {
    loadCalendarData()
  }, [currentDate, refreshTrigger, loadCalendarData])

  // Refresh calendar when user authentication state changes
  useEffect(() => {
    loadCalendarData()
  }, [user]) // eslint-disable-line react-hooks/exhaustive-deps

  // Close month/year picker when clicking outside
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (showMonthYearPicker && !target.closest('.month-year-picker')) {
        setShowMonthYearPicker(false)
      }
    }

    if (showMonthYearPicker) {
      document.addEventListener('mousedown', handleClickOutside)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
    }
  }, [showMonthYearPicker])

  const getStatsForDate = (date: Date) => {
    const dateStr = format(date, 'yyyy-MM-dd')
    return dailyStats.find(stat => stat.date === dateStr)
  }

  // Calculate weekly stats for a given week
  const getWeeklyStats = (weekStart: Date) => {
    const weekDays = []
    for (let i = 0; i < 7; i++) {
      const day = new Date(weekStart)
      day.setDate(weekStart.getDate() + i)
      weekDays.push(day)
    }

    let totalPnL = 0
    let totalRReturn = 0
    let totalTrades = 0
    let winningTrades = 0
    let losingTrades = 0

    weekDays.forEach(day => {
      const stats = getStatsForDate(day)
      if (stats && stats.trade_count > 0) {
        totalPnL += stats.total_profit_loss
        totalRReturn += stats.total_r_return
        totalTrades += stats.trade_count
        winningTrades += stats.winning_trades
        losingTrades += stats.losing_trades
      }
    })

    return {
      totalPnL,
      totalRReturn,
      totalTrades,
      winningTrades,
      losingTrades,
      hasData: totalTrades > 0
    }
  }

  // Get all weeks in the calendar
  const getWeeksInCalendar = () => {
    const weeks = []
    const currentWeekStart = new Date(calendarStart)

    while (currentWeekStart <= calendarEnd) {
      weeks.push(new Date(currentWeekStart))
      currentWeekStart.setDate(currentWeekStart.getDate() + 7)
    }

    return weeks
  }

  // Calculate monthly totals
  const getMonthlyStats = () => {
    const totalPnL = dailyStats.reduce((sum, stat) => sum + stat.total_profit_loss, 0)
    const totalRReturn = dailyStats.reduce((sum, stat) => sum + stat.total_r_return, 0)
    const totalTrades = dailyStats.reduce((sum, stat) => sum + stat.trade_count, 0)
    const winningDays = dailyStats.filter(stat => stat.total_profit_loss > 0).length
    const losingDays = dailyStats.filter(stat => stat.total_profit_loss < 0).length
    const tradingDays = dailyStats.length

    return {
      totalPnL,
      totalRReturn,
      totalTrades,
      winningDays,
      losingDays,
      tradingDays,
      winRate: tradingDays > 0 ? (winningDays / tradingDays) * 100 : 0
    }
  }

  const monthlyStats = getMonthlyStats()

  const navigateMonth = (direction: 'prev' | 'next') => {
    setCurrentDate(prev => {
      const newDate = new Date(prev)
      if (direction === 'prev') {
        newDate.setMonth(prev.getMonth() - 1)
      } else {
        newDate.setMonth(prev.getMonth() + 1)
      }
      return newDate
    })
  }

  const handleMonthYearChange = (month: number, year: number) => {
    setCurrentDate(new Date(year, month, 1))
    setShowMonthYearPicker(false)
  }

  const toggleMonthYearPicker = () => {
    setShowMonthYearPicker(!showMonthYearPicker)
  }

  // Generate year options (2010 through current year)
  const currentYear = new Date().getFullYear()
  const startYear = 2010
  const yearOptions = Array.from({ length: currentYear - startYear + 1 }, (_, i) => startYear + i)

  // Month names
  const monthNames = [
    'January', 'February', 'March', 'April', 'May', 'June',
    'July', 'August', 'September', 'October', 'November', 'December'
  ]



  return (
    <div className="w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-2 sm:p-4 lg:p-6">
      {/* Header */}
      <div className="flex flex-col space-y-4 mb-4 sm:flex-row sm:items-center sm:justify-between sm:space-y-0 sm:mb-6">
        <div className="flex items-center justify-center space-x-2 sm:space-x-4 relative">
          <button
            type="button"
            onClick={() => navigateMonth('prev')}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
            title="Previous month"
          >
            <ChevronLeft className="w-5 h-5 text-gray-600 dark:text-gray-300" />
          </button>

          {/* Clickable Month/Year */}
          <button
            type="button"
            onClick={toggleMonthYearPicker}
            className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 dark:text-white hover:text-blue-600 dark:hover:text-blue-400 transition-colors cursor-pointer px-2 py-1 rounded hover:bg-gray-50 dark:hover:bg-gray-700"
            title="Click to select month and year"
          >
            {format(currentDate, 'MMMM yyyy')}
          </button>

          {/* Month/Year Picker Dropdown */}
          {showMonthYearPicker && (
            <div className="month-year-picker absolute top-full left-1/2 transform -translate-x-1/2 mt-2 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg shadow-lg z-10 p-3 sm:p-4 w-[280px] sm:min-w-[300px]">
              <div className="flex flex-col space-y-3 sm:flex-row sm:space-y-0 sm:space-x-4">
                {/* Month Selector */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Month</label>
                  <select
                    value={currentDate.getMonth()}
                    onChange={(e) => handleMonthYearChange(parseInt(e.target.value), currentDate.getFullYear())}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    title="Select month"
                    aria-label="Select month"
                  >
                    {monthNames.map((month, index) => (
                      <option key={index} value={index}>
                        {month}
                      </option>
                    ))}
                  </select>
                </div>

                {/* Year Selector */}
                <div className="flex-1">
                  <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2">Year</label>
                  <select
                    value={currentDate.getFullYear()}
                    onChange={(e) => handleMonthYearChange(currentDate.getMonth(), parseInt(e.target.value))}
                    className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
                    title="Select year"
                    aria-label="Select year"
                  >
                    {yearOptions.map((year) => (
                      <option key={year} value={year}>
                        {year}
                      </option>
                    ))}
                  </select>
                </div>
              </div>

              {/* Close Button */}
              <div className="mt-4 flex justify-end">
                <button
                  type="button"
                  onClick={() => setShowMonthYearPicker(false)}
                  className="px-4 py-2 text-sm bg-gray-100 dark:bg-gray-600 text-gray-700 dark:text-gray-300 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
                >
                  Close
                </button>
              </div>
            </div>
          )}

          <button
            type="button"
            onClick={() => navigateMonth('next')}
            className="p-2 hover:bg-gray-100 rounded-full transition-colors"
            title="Next month"
          >
            <ChevronRight className="w-5 h-5" />
          </button>
        </div>


      </div>

      {/* Monthly Stats */}
      {monthlyStats.tradingDays > 0 && (
        <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-3 sm:p-4 mb-4 sm:mb-6">
          <h3 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-3 text-center sm:text-left">
            {format(currentDate, 'MMMM yyyy')} Summary
          </h3>
          <div className="grid grid-cols-2 lg:grid-cols-4 gap-3 sm:gap-4">
            <div className="text-center">
              <div className={`text-lg sm:text-xl lg:text-2xl font-bold ${monthlyStats.totalPnL >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {monthlyStats.totalPnL >= 0 ? '+$' : '-$'}{Math.abs(monthlyStats.totalPnL).toFixed(2)}
              </div>
              <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Total P&L</div>
            </div>

            <div className="text-center">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">
                {monthlyStats.totalTrades}
              </div>
              <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Total Trades</div>
            </div>
            <div className="text-center">
              <div className="text-lg sm:text-xl lg:text-2xl font-bold text-gray-900 dark:text-white">
                {monthlyStats.tradingDays}
              </div>
              <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Trading Days</div>
            </div>
            <div className="text-center">
              <div className={`text-lg sm:text-xl lg:text-2xl font-bold ${monthlyStats.winRate >= 50 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {monthlyStats.winRate.toFixed(1)}%
              </div>
              <div className="text-xs sm:text-sm text-gray-500 dark:text-gray-400">Daily Win Rate</div>
            </div>
          </div>
        </div>
      )}

      {/* Calendar Grid */}
      <div className="w-full grid grid-cols-8 gap-0.5 sm:gap-1">
        {/* Day headers */}
        {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day) => (
          <div key={day} className="p-2 sm:p-3 text-center text-xs sm:text-sm font-medium text-gray-500">
            <span className="hidden sm:inline">{day}</span>
            <span className="sm:hidden">{day.charAt(0)}</span>
          </div>
        ))}
        {/* Weekly totals header */}
        <div className="p-2 sm:p-3 text-center text-xs sm:text-sm font-medium text-gray-500 bg-gray-100 dark:bg-gray-600">
          <span className="hidden sm:inline">Weekly</span>
          <span className="sm:hidden">W</span>
        </div>

        {/* Calendar days and weekly totals */}
        {getWeeksInCalendar().map((weekStart) => {
          const weekDays = []
          for (let i = 0; i < 7; i++) {
            const day = new Date(weekStart)
            day.setDate(weekStart.getDate() + i)
            weekDays.push(day)
          }

          const weeklyStats = getWeeklyStats(weekStart)

          return (
            <React.Fragment key={weekStart.toISOString()}>
              {/* Render the 7 days of the week */}
              {weekDays.map(day => {
                const stats = getStatsForDate(day)
                const isCurrentMonth = isSameMonth(day, currentDate)
                const isTodayDate = isToday(day)
                const hasData = stats && stats.trade_count > 0

                return (
                  <div
                    key={day.toISOString()}
                    onClick={() => onDateClick(format(day, 'yyyy-MM-dd'))}
                    className={`
                      min-h-[60px] sm:min-h-[80px] lg:min-h-[100px] p-1 sm:p-2 border border-gray-200 dark:border-gray-600 cursor-pointer transition-colors
                      ${isCurrentMonth ? 'bg-white dark:bg-gray-700 hover:bg-gray-50 dark:hover:bg-gray-600' : 'bg-gray-50 dark:bg-gray-800 text-gray-400 dark:text-gray-500'}
                      ${isTodayDate ? 'ring-1 sm:ring-2 ring-blue-500' : ''}
                      ${hasData ? 'bg-blue-50 dark:bg-blue-900/30 hover:bg-blue-100 dark:hover:bg-blue-900/50' : ''}
                    `}
                  >
                    <div className="flex flex-col h-full">
                      <div className={`text-xs sm:text-sm font-medium ${isTodayDate ? 'text-blue-600 dark:text-blue-400' : 'text-gray-900 dark:text-white'}`}>
                        {format(day, 'd')}
                      </div>

                      {hasData && (
                        <div className="flex-1 mt-1">
                          <div className={`text-xs font-medium ${
                            stats.total_profit_loss >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                          }`}>
                            <span className="hidden sm:inline">{stats.total_profit_loss >= 0 ? '+$' : '-$'}{Math.abs(stats.total_profit_loss).toFixed(2)}</span>
                            <span className="sm:hidden">{stats.total_profit_loss >= 0 ? '+$' : '-$'}{Math.abs(stats.total_profit_loss).toFixed(0)}</span>
                          </div>
                          <div className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                            <span className="hidden sm:inline">{stats.trade_count} trade{stats.trade_count !== 1 ? 's' : ''}</span>
                            <span className="sm:hidden">{stats.trade_count}T</span>
                          </div>
                          <div className="hidden lg:block">
                            {stats.winning_trades > 0 && (
                              <div className="text-xs text-green-600 dark:text-green-400">
                                W: {stats.winning_trades}
                              </div>
                            )}
                            {stats.losing_trades > 0 && (
                              <div className="text-xs text-red-600 dark:text-red-400">
                                L: {stats.losing_trades}
                              </div>
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                  </div>
                )
              })}

              {/* Weekly totals column */}
              <div className={`
                min-h-[60px] sm:min-h-[80px] lg:min-h-[100px] p-1 sm:p-2 border border-gray-200 dark:border-gray-600 bg-gray-100 dark:bg-gray-600
                ${weeklyStats.hasData ? 'bg-blue-100 dark:bg-blue-800/50' : ''}
              `}>
                {weeklyStats.hasData && (
                  <div className="flex flex-col h-full text-center">
                    <div className={`text-xs font-bold ${
                      weeklyStats.totalPnL >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
                    }`}>
                      <span className="hidden sm:inline">{weeklyStats.totalPnL >= 0 ? '+$' : '-$'}{Math.abs(weeklyStats.totalPnL).toFixed(2)}</span>
                      <span className="sm:hidden">{weeklyStats.totalPnL >= 0 ? '+$' : '-$'}{Math.abs(weeklyStats.totalPnL).toFixed(0)}</span>
                    </div>

                    <div className="text-xs text-gray-600 dark:text-gray-300 mt-1">
                      <span className="hidden sm:inline">{weeklyStats.totalTrades} trade{weeklyStats.totalTrades !== 1 ? 's' : ''}</span>
                      <span className="sm:hidden">{weeklyStats.totalTrades}T</span>
                    </div>
                    <div className="hidden lg:block mt-1">
                      {weeklyStats.winningTrades > 0 && (
                        <div className="text-xs text-green-600 dark:text-green-400">
                          W: {weeklyStats.winningTrades}
                        </div>
                      )}
                      {weeklyStats.losingTrades > 0 && (
                        <div className="text-xs text-red-600 dark:text-red-400">
                          L: {weeklyStats.losingTrades}
                        </div>
                      )}
                    </div>
                  </div>
                )}
              </div>
            </React.Fragment>
          )
        })}
      </div>

      {loading && (
        <div className="absolute inset-0 bg-white dark:bg-gray-800 bg-opacity-75 dark:bg-opacity-90 flex items-center justify-center">
          <div className="text-gray-500 dark:text-gray-400">Loading calendar data...</div>
        </div>
      )}
    </div>
  )
}
