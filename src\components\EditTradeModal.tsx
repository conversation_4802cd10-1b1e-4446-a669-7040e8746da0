﻿'use client'

import { useState, useEffect } from 'react'
import { useForm } from 'react-hook-form'
import { zodResolver } from '@hookform/resolvers/zod'
import { X } from 'lucide-react'
import { Trade, TradeSetup, SupportType } from '@/lib/supabase'
import { getTradeSetups, getSupportTypes } from '@/lib/database'
import { normalizeDateTimeLocal, formatDateTimeLocalField } from '@/lib/datetime'
import OptionTradeForm, { optionTradeSchema, OptionTradeFormData } from './OptionTradeForm'

interface TradeUpdatedPayload {
  previousTrade: Trade
  updates: Partial<Trade>
}

interface EditTradeModalProps {
  isOpen: boolean
  onClose: () => void
  onTradeUpdated: (payload: TradeUpdatedPayload) => void
  trade: Trade | null
}

export default function EditTradeModal({ isOpen, onClose, onTradeUpdated, trade }: EditTradeModalProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [tradeSetups, setTradeSetups] = useState<TradeSetup[]>([])
  const [supportTypes, setSupportTypes] = useState<SupportType[]>([])

  // Load dropdown data when modal opens
  useEffect(() => {
    if (isOpen) {
      loadDropdownData()
    }
  }, [isOpen])

  const loadDropdownData = async () => {
    try {
      const [setupsData, typesData] = await Promise.all([
        getTradeSetups(),
        getSupportTypes()
      ])
      setTradeSetups(setupsData)
      setSupportTypes(typesData)
    } catch (error) {
      console.error('Error loading dropdown data:', error)
    }
  }

  // Option trade form
  const form = useForm<OptionTradeFormData>({
    resolver: zodResolver(optionTradeSchema),
  })

  // Populate form when trade changes
  useEffect(() => {
    if (trade && isOpen) {
      form.setValue('status', trade.status)
      form.setValue('entry_datetime', formatDateTimeLocalField(trade.entry_datetime))
      form.setValue('exit_datetime', formatDateTimeLocalField(trade.exit_datetime))
      form.setValue('ticker', trade.ticker)
      form.setValue('option_type', trade.option_type!)
      form.setValue('option_strike', trade.option_strike!)
      form.setValue('option_expiration', trade.option_expiration!)
      form.setValue('contracts', trade.contracts!)
      form.setValue('entry_price', trade.entry_price)
      form.setValue('exit_price', trade.exit_price || undefined)
      form.setValue('mae_price', trade.mae_price || undefined)
      form.setValue('mfe_price', trade.mfe_price || undefined)
      form.setValue('trade_setup', trade.trade_setup || undefined)
      form.setValue('support_type', trade.support_type || undefined)
      form.setValue('price_to_vwap_position', trade.price_to_vwap_position || undefined)
    }
  }, [trade, isOpen, form])

  const onSubmit = async (data: OptionTradeFormData) => {
    if (!trade) return

    try {
      setIsSubmitting(true)
      const previousTrade: Trade = { ...trade }

      const tradeData: Partial<Trade> = {
        status: data.status,
        entry_datetime: normalizeDateTimeLocal(data.entry_datetime) || '',
        exit_datetime: data.exit_datetime ? normalizeDateTimeLocal(data.exit_datetime) : null,
        ticker: data.ticker,
        option_type: data.option_type,
        option_strike: data.option_strike,
        option_expiration: data.option_expiration,
        contracts: data.contracts,
        entry_price: data.entry_price,
        exit_price: data.exit_price || null,
        mae_price: data.mae_price || null,
        mfe_price: data.mfe_price || null,
      }

      form.reset()
      onTradeUpdated({ previousTrade, updates: tradeData })
      onClose()
    } catch (error) {
      console.error('Error preparing trade update:', error)
      alert('Failed to update trade. Please check your input and try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleClose = () => {
    form.reset()
    onClose()
  }

  if (!isOpen || !trade) return null

  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50 p-2 sm:p-4">
      <div className="bg-white dark:bg-gray-800 rounded-lg shadow-xl max-w-2xl w-full max-h-[95vh] sm:max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-600">
          <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">Edit Trade</h2>
          <button
            type="button"
            onClick={handleClose}
            className="p-2 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-full transition-colors"
            title="Close modal"
          >
            <X className="w-5 h-5 text-gray-500 dark:text-gray-400" />
          </button>
        </div>

        <div className="p-4 sm:p-6">
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
            <OptionTradeForm
              register={form.register}
              watch={form.watch}
              setValue={form.setValue}
              errors={form.formState.errors}
              tradeSetups={tradeSetups}
              supportTypes={supportTypes}
            />

            <div className="flex flex-col sm:flex-row sm:justify-end space-y-2 sm:space-y-0 sm:space-x-3 pt-4 border-t border-gray-200 dark:border-gray-600">
              <button
                type="button"
                onClick={handleClose}
                className="w-full sm:w-auto px-4 py-2 text-gray-700 dark:text-gray-300 bg-gray-100 dark:bg-gray-600 rounded-md hover:bg-gray-200 dark:hover:bg-gray-500 transition-colors"
              >
                Cancel
              </button>
              <button
                type="submit"
                disabled={isSubmitting}
                className="w-full sm:w-auto px-4 py-2 bg-blue-600 dark:bg-blue-500 text-white rounded-md hover:bg-blue-700 dark:hover:bg-blue-600 disabled:opacity-50 disabled:cursor-not-allowed transition-colors"
              >
                {isSubmitting ? 'Updating...' : 'Update Trade'}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  )
}
