'use client'

import { useEffect } from 'react'
import { UseFormRegister, UseFormWatch, UseFormSetValue, FieldErrors } from 'react-hook-form'
import { z } from 'zod'
import { TradeSetup, SupportType } from '@/lib/supabase'

// Option trade schema
export const optionTradeSchema = z.object({
  status: z.enum(['Open', 'Closed'], {
    required_error: 'Status is required',
    invalid_type_error: 'Status must be either Open or Closed'
  }),
  entry_datetime: z.string()
    .min(1, 'Entry date/time is required')
    .regex(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/, 'Invalid date/time format'),
  exit_datetime: z.string()
    .optional()
    .refine((val) => !val || /^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}$/.test(val), 'Invalid date/time format'),
  ticker: z.string()
    .min(1, 'Ticker is required')
    .max(10, 'Ticker must be 10 characters or less')
    .regex(/^[A-Z0-9]+$/, 'Ticker must contain only letters and numbers')
    .transform(val => val.toUpperCase().trim()),
  option_type: z.enum(['Call', 'Put'], {
    required_error: 'Option type is required',
    invalid_type_error: 'Option type must be either Call or Put'
  }),
  option_strike: z.number()
    .positive('Strike price must be positive')
    .max(99999.99, 'Strike price too large'),
  option_expiration: z.string()
    .min(1, 'Option expiration is required')
    .regex(/^\d{4}-\d{2}-\d{2}$/, 'Invalid date format'),
  contracts: z.number()
    .int('Number of contracts must be a whole number')
    .positive('Number of contracts must be a positive integer')
    .max(10000, 'Number of contracts too large'),
  entry_price: z.number()
    .positive('Entry price must be positive')
    .max(99999.99, 'Entry price too large'),
  exit_price: z.number()
    .positive('Exit price must be positive')
    .max(99999.99, 'Exit price too large')
    .optional(),
  mae_price: z.number()
    .positive('MAE price must be positive')
    .max(99999.99, 'MAE price too large')
    .optional(),
  mfe_price: z.number()
    .positive('MFE price must be positive')
    .max(99999.99, 'MFE price too large')
    .optional(),
  trade_setup: z.string()
    .max(100, 'Trade setup must be 100 characters or less')
    .optional(),
  support_type: z.string()
    .max(100, 'Support type must be 100 characters or less')
    .optional(),
  price_to_vwap_position: z.enum(['Above', 'At', 'Below'], {
    invalid_type_error: 'Price relative to VWAP must be Above, At, or Below'
  }).optional(),
}).refine((data) => {
  // For Closed trades, require entry_price and exit_price
  if (data.status === 'Closed') {
    return data.entry_price && data.exit_price;
  }
  // For Open trades, require entry_price
  if (data.status === 'Open') {
    return data.entry_price;
  }
  return true;
}, {
  message: 'Closed trades require entry price and exit price. Open trades require entry price.',
  path: ['status']
})

export type OptionTradeFormData = z.infer<typeof optionTradeSchema>

interface OptionTradeFormProps {
  register: UseFormRegister<OptionTradeFormData>
  watch: UseFormWatch<OptionTradeFormData>
  setValue: UseFormSetValue<OptionTradeFormData>
  errors: FieldErrors<OptionTradeFormData>
  tradeSetups?: TradeSetup[]
  supportTypes?: SupportType[]
}

export default function OptionTradeForm({ register, watch, setValue, errors, tradeSetups = [], supportTypes = [] }: OptionTradeFormProps) {
  const exitDateTime = watch('exit_datetime')
  const entryDateTime = watch('entry_datetime')
  const contracts = watch('contracts') || 1
  const calculatedFees = contracts * 1.30

  // Format price to 2 decimal places
  const formatPrice = (value: string | number) => {
    const numValue = typeof value === 'string' ? parseFloat(value) : value
    if (isNaN(numValue)) return ''
    return numValue.toFixed(2)
  }

  // Handle price field blur to format to 2 decimal places
  const handlePriceBlur = (fieldName: 'entry_price' | 'exit_price' | 'mae_price' | 'mfe_price', value: string) => {
    if (value && !isNaN(parseFloat(value))) {
      const formattedValue = formatPrice(value)
      setValue(fieldName, parseFloat(formattedValue))
      // Update the actual input element to show formatted value
      const input = document.querySelector(`input[name="${fieldName}"]`) as HTMLInputElement
      if (input) {
        input.value = formattedValue
      }
    }
  }

  // Auto-populate option expiration and exit datetime with entry date when entry_datetime changes
  // Do not overwrite an existing exit_datetime (e.g., when editing)
  useEffect(() => {
    if (entryDateTime) {
      // Extract date part from datetime-local format (YYYY-MM-DDTHH:mm)
      const dateOnly = entryDateTime.split('T')[0]
      setValue('option_expiration', dateOnly)

      // Only set exit datetime if not already provided
      if (!exitDateTime) {
        setValue('exit_datetime', entryDateTime)
      }
    }
  }, [entryDateTime, exitDateTime, setValue])

  // Handle price field focus to allow easy editing
  const handlePriceFocus = (fieldName: 'entry_price' | 'exit_price' | 'mae_price' | 'mfe_price') => {
    const input = document.querySelector(`input[name="${fieldName}"]`) as HTMLInputElement
    if (input) {
      // Select all text for easy replacement
      input.select()
    }
  }

  return (
    <div className="space-y-4">
      {/* Date/Time Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Entry Date/Time */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Entry Date/Time *
          </label>
          <input
            type="datetime-local"
            {...register('entry_datetime')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.entry_datetime && (
            <p className="text-red-500 text-sm mt-1">{errors.entry_datetime.message}</p>
          )}
        </div>

        {/* Exit Date/Time */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Exit Date/Time
          </label>
          <input
            type="datetime-local"
            {...register('exit_datetime')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.exit_datetime && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.exit_datetime.message}</p>
          )}
        </div>
      </div>

      {/* Status and Ticker */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Status */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Status *
          </label>
          <select
            {...register('status')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select status</option>
            <option value="Open">Open</option>
            <option value="Closed">Closed</option>
          </select>
          {errors.status && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.status.message}</p>
          )}
        </div>

        {/* Ticker */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Ticker *
          </label>
          <input
            type="text"
            placeholder="e.g., SPY, QQQ, AAPL"
            {...register('ticker')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.ticker && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.ticker.message}</p>
          )}
        </div>
      </div>

      {/* Option Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {/* Option Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Option Type *
          </label>
          <select
            {...register('option_type')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select type</option>
            <option value="Call">Call</option>
            <option value="Put">Put</option>
          </select>
          {errors.option_type && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.option_type.message}</p>
          )}
        </div>

        {/* Option Strike */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Option Strike *
          </label>
          <input
            type="number"
            step="0.01"
            placeholder="e.g., 450.00"
            {...register('option_strike', { valueAsNumber: true })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.option_strike && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.option_strike.message}</p>
          )}
        </div>

        {/* Option Expiration */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Option Expiration *
          </label>
          <input
            type="date"
            {...register('option_expiration')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.option_expiration && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.option_expiration.message}</p>
          )}
        </div>
      </div>

      {/* Contracts and Fees */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Number of Contracts */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Number of Contracts *
          </label>
          <input
            type="number"
            min="1"
            {...register('contracts', { valueAsNumber: true })}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.contracts && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.contracts.message}</p>
          )}
        </div>

        {/* Fees */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Total Fees
          </label>
          <input
            type="text"
            value={`$${calculatedFees.toFixed(2)}`}
            readOnly
            title="Total fees calculated at $1.30 per contract"
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-gray-50 dark:bg-gray-600 text-gray-600 dark:text-gray-300"
          />
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Calculated at $1.30 per contract
          </p>
        </div>
      </div>

      {/* Trade Analysis Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
        {/* Trade Setup */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Trade Setup
          </label>
          <select
            {...register('trade_setup')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select trade setup</option>
            {tradeSetups.map((setup) => (
              <option key={setup.id} value={setup.name}>
                {setup.name}
              </option>
            ))}
          </select>
          {errors.trade_setup && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.trade_setup.message}</p>
          )}
        </div>

        {/* Support Type */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Support Type
          </label>
          <select
            {...register('support_type')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select support type</option>
            {supportTypes.map((type) => (
              <option key={type.id} value={type.name}>
                {type.name}
              </option>
            ))}
          </select>
          {errors.support_type && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.support_type.message}</p>
          )}
        </div>

        {/* Price Relative to VWAP */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Price Relative to VWAP
          </label>
          <select
            {...register('price_to_vwap_position')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
          >
            <option value="">Select position</option>
            <option value="Above">Above</option>
            <option value="At">At</option>
            <option value="Below">Below</option>
          </select>
          {errors.price_to_vwap_position && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.price_to_vwap_position.message}</p>
          )}
        </div>
      </div>

      {/* Price Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* Entry Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Entry Price *
          </label>
          <input
            type="text"
            inputMode="decimal"
            placeholder="e.g., 2.50"
            {...register('entry_price', {
              valueAsNumber: true,
              validate: (value) => {
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                if (isNaN(num)) return 'Please enter a valid price'
                if (num <= 0) return 'Price must be greater than 0'
                if (num > 99999.99) return 'Price too large'
                return true
              }
            })}
            onBlur={(e) => handlePriceBlur('entry_price', e.target.value)}
            onFocus={() => handlePriceFocus('entry_price')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.entry_price && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.entry_price.message}</p>
          )}
        </div>

        {/* Exit Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            Exit Price {exitDateTime ? '*' : ''}
          </label>
          <input
            type="text"
            inputMode="decimal"
            placeholder="e.g., 3.20"
            {...register('exit_price', {
              setValueAs: (value) => {
                if (value === '' || value === null || value === undefined) return undefined
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                return Number.isNaN(num) ? undefined : num
              },
              validate: (value) => {
                if (!value) return true // Optional field
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                if (isNaN(num)) return 'Please enter a valid price'
                if (num <= 0) return 'Price must be greater than 0'
                if (num > 99999.99) return 'Price too large'
                return true
              }
            })}
            onBlur={(e) => handlePriceBlur('exit_price', e.target.value)}
            onFocus={() => handlePriceFocus('exit_price')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.exit_price && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.exit_price.message}</p>
          )}
        </div>
      </div>

      {/* MAE and MFE Price Fields */}
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
        {/* MAE Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            MAE Price (Optional)
          </label>
          <input
            type="text"
            inputMode="decimal"
            placeholder="e.g., 1.80"
            {...register('mae_price', {
              setValueAs: (value) => {
                if (value === '' || value === null || value === undefined) return undefined
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                return Number.isNaN(num) ? undefined : num
              },
              validate: (value) => {
                if (!value) return true // Optional field
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                if (isNaN(num)) return 'Please enter a valid price'
                if (num <= 0) return 'Price must be greater than 0'
                if (num > 99999.99) return 'Price too large'
                return true
              }
            })}
            onBlur={(e) => handlePriceBlur('mae_price', e.target.value)}
            onFocus={() => handlePriceFocus('mae_price')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.mae_price && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.mae_price.message}</p>
          )}
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Maximum Adverse Excursion - worst price during the trade
          </p>
        </div>

        {/* MFE Price */}
        <div>
          <label className="block text-sm font-medium text-gray-700 dark:text-gray-300 mb-1">
            MFE Price (Optional)
          </label>
          <input
            type="text"
            inputMode="decimal"
            placeholder="e.g., 3.50"
            {...register('mfe_price', {
              setValueAs: (value) => {
                if (value === '' || value === null || value === undefined) return undefined
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                return Number.isNaN(num) ? undefined : num
              },
              validate: (value) => {
                if (!value) return true // Optional field
                const num = typeof value === 'number' ? value : parseFloat(String(value))
                if (isNaN(num)) return 'Please enter a valid price'
                if (num <= 0) return 'Price must be greater than 0'
                if (num > 99999.99) return 'Price too large'
                return true
              }
            })}
            onBlur={(e) => handlePriceBlur('mfe_price', e.target.value)}
            onFocus={() => handlePriceFocus('mfe_price')}
            className="w-full px-3 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500"
          />
          {errors.mfe_price && (
            <p className="text-red-500 dark:text-red-400 text-sm mt-1">{errors.mfe_price.message}</p>
          )}
          <p className="text-xs text-gray-500 dark:text-gray-400 mt-1">
            Maximum Favorable Excursion - best price during the trade
          </p>
        </div>
      </div>
    </div>
  )
}
