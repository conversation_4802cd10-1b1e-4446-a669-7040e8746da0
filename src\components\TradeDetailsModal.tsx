'use client'

import { useEffect, useMemo, useState } from 'react'
import { X, Pencil } from 'lucide-react'
import type { Trade } from '@/lib/supabase'
import { fetchCandlesForTrade } from '@/lib/marketData'
import dynamic from 'next/dynamic'

const TradeCandlestickChart = dynamic(() => import('./TradeCandlestickChart'), { ssr: false })

interface TradeDetailsModalProps {
  isOpen: boolean
  onClose: () => void
  trade: Trade | null
  onEditTrade?: (trade: Trade) => void
}

const TIMEFRAME_OPTIONS: Array<'1m' | '5m' | '15m'> = ['1m', '5m', '15m']

export default function TradeDetailsModal({ isOpen, onClose, trade, onEditTrade }: TradeDetailsModalProps) {
  const [loading, setLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [candles, setCandles] = useState<{ time: number; open: number; high: number; low: number; close: number }[]>([])
  const [timeframe, setTimeframe] = useState<'1m' | '5m' | '15m'>('5m')

  const entryUnix = useMemo(() => (trade ? Math.floor(new Date(trade.entry_datetime).getTime() / 1000) : undefined), [trade])
  const exitUnix = useMemo(() => (trade?.exit_datetime ? Math.floor(new Date(trade.exit_datetime).getTime() / 1000) : null), [trade])
  const intervalSeconds = useMemo(() => {
    switch (timeframe) {
      case '1m':
        return 60
      case '15m':
        return 900
      default:
        return 300
    }
  }, [timeframe])

  useEffect(() => {
    let cancelled = false
    if (!isOpen || !trade) return

    setLoading(true)
    setError(null)
    fetchCandlesForTrade(trade, timeframe)
      .then((data) => {
        if (cancelled) return
        setCandles(data)
      })
      .catch((e) => {
        if (cancelled) return
        setError(e?.message || 'Failed to load market data')
      })
      .finally(() => {
        if (cancelled) return
        setLoading(false)
      })

    return () => {
      cancelled = true
    }
  }, [isOpen, trade, timeframe])

  const handleEdit = () => {
    if (!trade || !onEditTrade) return
    onClose()
    onEditTrade(trade)
  }

  if (!isOpen || !trade) return null

  // Calculate MAE and MFE metrics for option trades
  const calculateMaeMetrics = () => {
    if (!trade.mae_price || !trade.entry_price || !trade.contracts) return null
    const priceChange = trade.mae_price - trade.entry_price
    const perContractAmount = priceChange * 100
    const totalDollarAmount = perContractAmount * trade.contracts
    const percentageChange = (priceChange / trade.entry_price) * 100
    return { totalDollarAmount, perContractAmount, percentageChange }
  }

  const calculateMfeMetrics = () => {
    if (!trade.mfe_price || !trade.entry_price || !trade.contracts) return null
    const priceChange = trade.mfe_price - trade.entry_price
    const perContractAmount = priceChange * 100
    const totalDollarAmount = perContractAmount * trade.contracts
    const percentageChange = (priceChange / trade.entry_price) * 100
    return { totalDollarAmount, perContractAmount, percentageChange }
  }

  const maeMetrics = calculateMaeMetrics()
  const mfeMetrics = calculateMfeMetrics()

  return (
    <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50" role="dialog" aria-modal="true">
      <div className="relative w-full max-w-4xl bg-white dark:bg-gray-800 rounded-lg shadow-xl max-h-[90vh] overflow-y-auto">
        <div className="flex items-center justify-between p-4 sm:p-6 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-lg sm:text-xl font-semibold text-gray-900 dark:text-white">{trade.ticker} Trade Details</h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">{new Date(trade.entry_datetime).toLocaleString()}</p>
          </div>
          <div className="flex items-center space-x-2">
            {onEditTrade && (
              <button
                type="button"
                onClick={handleEdit}
                className="p-2 rounded-md hover:bg-gray-100 dark:hover:bg-gray-700"
                title="Edit trade"
              >
                <Pencil className="w-5 h-5 text-gray-600 dark:text-gray-300" />
              </button>
            )}
            <button
              type="button"
              onClick={onClose}
              className="p-2 rounded-full hover:bg-gray-100 dark:hover:bg-gray-700"
              title="Close"
            >
              <X className="w-5 h-5 text-gray-600 dark:text-gray-300" />
            </button>
          </div>
        </div>

        <div className="p-4 sm:p-6 space-y-6">
          <div>
            <div className="mb-3 flex flex-col gap-3 sm:flex-row sm:items-center sm:justify-between sm:gap-4">
              <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200">
                Price Chart ({timeframe.toUpperCase()})
              </h3>
              <div className="inline-flex overflow-hidden rounded-md border border-gray-200 bg-white text-xs shadow-sm dark:border-gray-700 dark:bg-gray-900">
                {TIMEFRAME_OPTIONS.map(option => (
                  <button
                    key={option}
                    type="button"
                    onClick={() => setTimeframe(option)}
                    className={`px-3 py-1.5 transition-colors ${
                      timeframe === option
                        ? 'bg-blue-600 text-white dark:bg-blue-500'
                        : 'text-gray-600 hover:bg-gray-100 dark:text-gray-300 dark:hover:bg-gray-700'
                    }`}
                  >
                    {option.toUpperCase()}
                  </button>
                ))}
              </div>
            </div>
            <div className="rounded-md border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-900 p-2">
              {loading ? (
                <div className="h-64 flex items-center justify-center text-gray-500">Loading chart...</div>
              ) : error ? (
                <div className="h-64 flex items-center justify-center text-gray-500">{error}</div>
              ) : candles.length === 0 ? (
                <div className="h-64 flex items-center justify-center text-gray-500">No data for window</div>
              ) : (
                <TradeCandlestickChart
                  candles={candles}
                  entryTime={entryUnix}
                  exitTime={exitUnix}
                  intervalSeconds={intervalSeconds}
                />
              )}
            </div>
          </div>

          <div>
            <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3">Summary</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
              <Info label="Status" value={trade.status} />
              <Info label="Ticker" value={trade.ticker} />
              <Info label="Type" value={trade.trade_type} />
              <Info label="Entry Price" value={formatCurrency(trade.entry_price)} />
              <Info label="Exit Price" value={formatCurrency(trade.exit_price)} />
              <Info label="P/L" value={formatCurrency(trade.profit_loss)} emphasis />
              <Info label="Return" value={formatPercent(trade.percentage_return)} />
              <Info label="Contracts" value={fmt(trade.contracts)} />
              <Info label="Strike" value={fmt(trade.option_strike)} />
              <Info label="Call/Put" value={trade.option_type || '-'} />
              <Info label="Expiration" value={trade.option_expiration || '-'} />
              <Info label="Fees" value={formatCurrency(trade.fees)} />
              <Info label="Trade Setup" value={trade.trade_setup || '-'} />
              <Info label="Support Type" value={trade.support_type || '-'} />
              <Info label="Price vs VWAP" value={trade.price_to_vwap_position || '-'} />
            </div>
          </div>

          {/* MAE and MFE Section */}
          {(trade.mae_price || trade.mfe_price) && (
            <div className="mt-6">
              <h3 className="text-sm font-semibold text-gray-800 dark:text-gray-200 mb-3">
                Maximum Excursion Analysis
              </h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
                {/* MAE Section */}
                {trade.mae_price && maeMetrics && (
                  <>
                    <Info label="MAE Price" value={formatCurrency(trade.mae_price)} />
                    <Info
                      label="MAE $ Total"
                      value={formatCurrency(maeMetrics.totalDollarAmount)}
                      emphasis={maeMetrics.totalDollarAmount < 0}
                    />
                    <Info
                      label="MAE $ Per Contract"
                      value={formatCurrency(maeMetrics.perContractAmount)}
                    />
                    <Info
                      label="MAE %"
                      value={formatPercent(maeMetrics.percentageChange)}
                    />
                  </>
                )}
                {/* MFE Section */}
                {trade.mfe_price && mfeMetrics && (
                  <>
                    <Info label="MFE Price" value={formatCurrency(trade.mfe_price)} />
                    <Info
                      label="MFE $ Total"
                      value={formatCurrency(mfeMetrics.totalDollarAmount)}
                      emphasis={mfeMetrics.totalDollarAmount > 0}
                    />
                    <Info
                      label="MFE $ Per Contract"
                      value={formatCurrency(mfeMetrics.perContractAmount)}
                    />
                    <Info
                      label="MFE %"
                      value={formatPercent(mfeMetrics.percentageChange)}
                    />
                  </>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  )
}

function Info({ label, value, emphasis }: { label: string; value: string; emphasis?: boolean }) {
  return (
    <div className="rounded border border-gray-200 dark:border-gray-700 p-3 bg-white dark:bg-gray-900">
      <div className="text-xs text-gray-500 dark:text-gray-400">{label}</div>
      <div className={`text-sm ${emphasis ? 'font-semibold' : ''} text-gray-900 dark:text-gray-200`}>{value}</div>
    </div>
  )
}

function formatCurrency(v: number | null | undefined) {
  if (v === null || v === undefined) return '-'
  return `$${Number(v).toFixed(2)}`
}

function formatPercent(v: number | null | undefined) {
  if (v === null || v === undefined) return '-'
  const sign = v >= 0 ? '+' : ''
  return `${sign}${v.toFixed(2)}%`
}

function fmt(v: number | string | null | undefined) {
  if (v === null || v === undefined) return '-'
  return String(v)
}

