'use client'

import { useState, useEffect, useCallback, useRef } from 'react'
import { Search, Filter, Edit, Trash2, Download } from 'lucide-react'
import { format } from 'date-fns'
import { formatDateTimeLocalField } from '@/lib/datetime'
import { getTrades, searchTrades, deleteTrade, updateTrade } from '@/lib/database'
import { Trade, isSupabaseConfigured } from '@/lib/supabase'
import { useAuth } from '@/contexts/AuthContext'

interface PendingEditAction {
  previousTrade: Trade
  updates: Partial<Trade>
}

interface TradeLogProps {
  refreshTrigger: number
  selectedDate?: string
  onEditTrade: (trade: Trade) => void
  onViewTrade?: (trade: Trade) => void
  pendingEditAction?: PendingEditAction | null
  onPendingEditHandled?: () => void
  onRefreshRequested?: () => void
}

const UNDO_TIMEOUT_MS = 5000
const MONTH_NAMES = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec']

const getNormalizedDate = (dateTime: string) => {
  const normalized = formatDateTimeLocalField(dateTime)
  return normalized ? normalized.split('T')[0] : ''
}

const formatDateTimeDisplay = (dateTime: string) => {
  const normalized = formatDateTimeLocalField(dateTime)
  if (!normalized) return '-'

  const [datePart, timePart = ''] = normalized.split('T')
  const [yearStr, monthStr, dayStr] = datePart.split('-')
  const [hourStr = '00', minuteStr = '00'] = timePart.split(':')

  const monthIndex = Number(monthStr) - 1
  const monthName = MONTH_NAMES[monthIndex] || monthStr

  const hours24 = Number(hourStr)
  if (Number.isNaN(hours24)) {
    return `${monthName} ${dayStr}, ${yearStr}`
  }

  const isPM = hours24 >= 12
  const hour12 = ((hours24 + 11) % 12) + 1

  return `${monthName} ${dayStr}, ${yearStr} ${hour12}:${minuteStr} ${isPM ? 'PM' : 'AM'}`
}

const formatShortDateDisplay = (dateTime: string) => {
  const normalized = formatDateTimeLocalField(dateTime)
  if (!normalized) return '-'

  const [datePart] = normalized.split('T')
  const [, monthStr, dayStr] = datePart.split('-')
  if (!monthStr || !dayStr) return datePart

  return `${monthStr}/${dayStr}`
}

const formatDateTimeRaw = (dateTime: string) => {
  const normalized = formatDateTimeLocalField(dateTime)
  if (!normalized) return dateTime || ''
  return normalized.replace('T', ' ')
}

const parseLocalDate = (dateString: string) => {
  if (!dateString) return new Date(NaN)
  const parts = dateString.split('-')
  if (parts.length < 3) return new Date(NaN)
  const [yearStr, monthStr, dayStr] = parts
  const year = Number(yearStr)
  const month = Number(monthStr)
  const day = Number(dayStr)
  if ([year, month, day].some(v => Number.isNaN(v))) return new Date(NaN)
  return new Date(year, month - 1, day)
}

type UndoState =
  | {
      type: 'delete'
      tradeBefore: Trade
      timeoutId: ReturnType<typeof setTimeout>
    }
  | {
      type: 'edit'
      tradeBefore: Trade
      updates: Partial<Trade>
      optimisticTrade: Trade
      timeoutId: ReturnType<typeof setTimeout>
    }

export default function TradeLog({
  refreshTrigger,
  selectedDate,
  onEditTrade,
  onViewTrade,
  pendingEditAction,
  onPendingEditHandled,
  onRefreshRequested,
}: TradeLogProps) {
  const { user } = useAuth()
  const [trades, setTrades] = useState<Trade[]>([])
  const [loading, setLoading] = useState(true)
  const [searchTerm, setSearchTerm] = useState('')
  const [sortBy, setSortBy] = useState<'entry_datetime' | 'ticker' | 'profit_loss' | 'percentage_return'>('entry_datetime')
  const [sortOrder, setSortOrder] = useState<'asc' | 'desc'>('desc')
  const [currentPage, setCurrentPage] = useState(1)
  const [pageSize, setPageSize] = useState(25)

  // Advanced filters
  const [dateRangeStart, setDateRangeStart] = useState('')
  const [dateRangeEnd, setDateRangeEnd] = useState('')
  const [profitLossFilter, setProfitLossFilter] = useState<'all' | 'winners' | 'losers' | 'open'>('all')
  const [optionTypeFilter, setOptionTypeFilter] = useState<'all' | 'Call' | 'Put'>('all')
  const [dayOfWeekFilter, setDayOfWeekFilter] = useState<'all' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday'>('all')
  const [minContracts, setMinContracts] = useState('')
  const [maxContracts, setMaxContracts] = useState('')
  const [showFilters, setShowFilters] = useState(false)
  const [undoState, setUndoState] = useState<UndoState | null>(null)
  const undoStateRef = useRef<UndoState | null>(null)

  const loadTrades = useCallback(async () => {
    try {
      setLoading(true)

      // If Supabase is configured but user is not signed in, show empty data
      if (isSupabaseConfigured && !user) {
        setTrades([])
        return
      }

      const data = await getTrades(100, 0)

      // Filter by selected date if provided
      let filteredData = data
      if (selectedDate) {
        filteredData = data.filter(trade => getNormalizedDate(trade.entry_datetime) === selectedDate)
      }

      setTrades(filteredData)
    } catch (error) {
      console.error('Error loading trades:', error)
      setTrades([])
    } finally {
      setLoading(false)
    }
  }, [selectedDate, user])

  const commitPendingAction = useCallback(async () => {
    const action = undoStateRef.current
    if (!action) {
      return
    }

    undoStateRef.current = null
    setUndoState(null)
    clearTimeout(action.timeoutId)

    try {
      if (action.type === 'delete') {
        await deleteTrade(action.tradeBefore.id)
        onRefreshRequested?.()
      } else if (action.type === 'edit') {
        const committedTrade = await updateTrade(action.tradeBefore.id, action.updates)
        setTrades(prevTrades =>
          prevTrades.map(trade => (trade.id === committedTrade.id ? committedTrade : trade))
        )
        onRefreshRequested?.()
      }
    } catch (error) {
      console.error('Error committing pending trade action:', error)
      alert('Error saving trade changes. Please try again.')
      setTrades(prevTrades =>
        prevTrades.map(trade => (trade.id === action.tradeBefore.id ? action.tradeBefore : trade))
      )
      await loadTrades()
    }
  }, [loadTrades, onRefreshRequested])

  const commitPendingActionRef = useRef(commitPendingAction)
  useEffect(() => {
    commitPendingActionRef.current = commitPendingAction
  }, [commitPendingAction])

  useEffect(() => {
    return () => {
      void commitPendingActionRef.current()
    }
  }, [])

  const handleSearch = useCallback(async () => {
    if (!searchTerm.trim()) {
      loadTrades()
      return
    }

    // If Supabase is configured but user is not signed in, show empty data
    if (isSupabaseConfigured && !user) {
      setTrades([])
      return
    }

    try {
      setLoading(true)
      const data = await searchTrades(searchTerm)
      setTrades(data)
    } catch (error) {
      console.error('Error searching trades:', error)
      setTrades([])
    } finally {
      setLoading(false)
    }
  }, [searchTerm, loadTrades, user])

  useEffect(() => {
    loadTrades()
  }, [refreshTrigger, selectedDate, loadTrades])

  // Refresh trades when user authentication state changes
  useEffect(() => {
    loadTrades()
  }, [user]) // eslint-disable-line react-hooks/exhaustive-deps

  useEffect(() => {
    if (searchTerm) {
      handleSearch()
    } else {
      loadTrades()
    }
  }, [searchTerm, handleSearch, loadTrades])

  useEffect(() => {
    if (!pendingEditAction) {
      return
    }

    const processEdit = async () => {
      await commitPendingActionRef.current()

      const optimisticTrade: Trade = {
        ...pendingEditAction.previousTrade,
        ...pendingEditAction.updates,
      }
      optimisticTrade.updated_at = new Date().toISOString()

      setTrades(prevTrades => {
        const exists = prevTrades.some(trade => trade.id === optimisticTrade.id)
        if (exists) {
          return prevTrades.map(trade =>
            trade.id === optimisticTrade.id ? optimisticTrade : trade
          )
        }
        return [...prevTrades, optimisticTrade]
      })

      const timeoutId = setTimeout(() => {
        void commitPendingActionRef.current()
      }, UNDO_TIMEOUT_MS)

      const nextUndoState: UndoState = {
        type: 'edit',
        tradeBefore: pendingEditAction.previousTrade,
        updates: pendingEditAction.updates,
        optimisticTrade,
        timeoutId,
      }

      undoStateRef.current = nextUndoState
      setUndoState(nextUndoState)
    }

    void processEdit().finally(() => {
      onPendingEditHandled?.()
    })
  }, [pendingEditAction, onPendingEditHandled])

  const handleDelete = async (id: string) => {
    if (!confirm('Are you sure you want to delete this trade?')) {
      return
    }

    const tradeToDelete = trades.find(trade => trade.id === id)
    if (!tradeToDelete) {
      return
    }

    await commitPendingActionRef.current()

    setTrades(prevTrades => prevTrades.filter(trade => trade.id !== id))

    const timeoutId = setTimeout(() => {
      void commitPendingActionRef.current()
    }, UNDO_TIMEOUT_MS)

    const nextUndoState: UndoState = {
      type: 'delete',
      tradeBefore: tradeToDelete,
      timeoutId,
    }

    undoStateRef.current = nextUndoState
    setUndoState(nextUndoState)
  }

  const handleUndo = useCallback(async () => {
    const action = undoStateRef.current
    if (!action) {
      return
    }

    clearTimeout(action.timeoutId)
    undoStateRef.current = null
    setUndoState(null)

    if (action.type === 'delete') {
      setTrades(prevTrades => {
        const exists = prevTrades.some(trade => trade.id === action.tradeBefore.id)
        if (exists) {
          return prevTrades
        }
        return [...prevTrades, action.tradeBefore]
      })
    } else if (action.type === 'edit') {
      setTrades(prevTrades =>
        prevTrades.map(trade =>
          trade.id === action.tradeBefore.id ? action.tradeBefore : trade
        )
      )
    }
  }, [])

  // Apply all filters
  const filteredTrades = trades.filter(trade => {
    // Search filter
    const matchesSearch = !searchTerm ||
      trade.ticker.toLowerCase().includes(searchTerm.toLowerCase())

    // Selected date filter (from calendar)
    const entryDate = getNormalizedDate(trade.entry_datetime)
    const matchesSelectedDate = !selectedDate || entryDate === selectedDate

    // Date range filter
    const matchesDateRange = (!dateRangeStart || (entryDate && entryDate >= dateRangeStart)) &&
                            (!dateRangeEnd || (entryDate && entryDate <= dateRangeEnd))

    // Profit/Loss filter
    let matchesProfitLoss = true
    if (profitLossFilter === 'winners') {
      matchesProfitLoss = trade.profit_loss !== null && trade.profit_loss > 0
    } else if (profitLossFilter === 'losers') {
      matchesProfitLoss = trade.profit_loss !== null && trade.profit_loss < 0
    } else if (profitLossFilter === 'open') {
      matchesProfitLoss = trade.profit_loss === null
    }

    // Option type filter
    const matchesOptionType = optionTypeFilter === 'all' || trade.option_type === optionTypeFilter

    // Day of week filter
    const tradeDayOfWeek = entryDate ? format(parseLocalDate(entryDate), 'EEEE') : ''
    const matchesDayOfWeek = dayOfWeekFilter === 'all' || tradeDayOfWeek === dayOfWeekFilter

    // Contracts filter (only applies to option trades)
    const matchesContracts = (!minContracts || (trade.contracts !== null && trade.contracts >= parseInt(minContracts))) &&
                            (!maxContracts || (trade.contracts !== null && trade.contracts <= parseInt(maxContracts)))

    return matchesSearch && matchesSelectedDate && matchesDateRange &&
           matchesProfitLoss && matchesOptionType && matchesDayOfWeek && matchesContracts
  })

  // Sort the filtered trades
  const sortedTrades = [...filteredTrades].sort((a, b) => {
    let aValue: string | number = ''
    let bValue: string | number = ''

    if (sortBy === 'entry_datetime') {
      aValue = getNormalizedDate(a.entry_datetime)
      bValue = getNormalizedDate(b.entry_datetime)
    } else if (sortBy === 'ticker') {
      aValue = (a[sortBy] as string)?.toLowerCase() || ''
      bValue = (b[sortBy] as string)?.toLowerCase() || ''
    } else if (sortBy === 'profit_loss' || sortBy === 'percentage_return') {
      aValue = (a[sortBy] as number) || 0
      bValue = (b[sortBy] as number) || 0
    }

    if (sortOrder === 'asc') {
      return aValue < bValue ? -1 : aValue > bValue ? 1 : 0
    } else {
      return aValue > bValue ? -1 : aValue < bValue ? 1 : 0
    }
  })

  // Pagination logic
  const totalTrades = sortedTrades.length
  const totalPages = Math.ceil(totalTrades / pageSize)
  const startIndex = (currentPage - 1) * pageSize
  const endIndex = startIndex + pageSize
  const paginatedTrades = sortedTrades.slice(startIndex, endIndex)

  // Reset to first page when filters change
  useEffect(() => {
    setCurrentPage(1)
  }, [searchTerm, selectedDate, dateRangeStart, dateRangeEnd, profitLossFilter, optionTypeFilter, dayOfWeekFilter, minContracts, maxContracts])

  // Reset to first page when page size changes
  useEffect(() => {
    setCurrentPage(1)
  }, [pageSize])

  // Change sort order to ascending when a specific date is selected to show earliest trades first
  useEffect(() => {
    if (selectedDate && sortBy === 'entry_datetime') {
      setSortOrder('asc')
    } else if (!selectedDate && sortBy === 'entry_datetime') {
      setSortOrder('desc')
    }
  }, [selectedDate, sortBy])

  const formatCurrency = (amount: number | null) => {
    if (amount === null) return '-'
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: 'USD',
      minimumFractionDigits: 2
    }).format(amount)
  }

  const formatDateTime = (dateTime: string) => formatDateTimeDisplay(dateTime)

  const getProfitLossColor = (profitLoss: number | null) => {
    if (profitLoss === null) return 'text-gray-500 dark:text-gray-400'
    return profitLoss >= 0 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'
  }

  // Calculate statistics for the currently displayed trades
  const calculateTradeStats = () => {
    const completedTrades = sortedTrades.filter(trade => trade.profit_loss !== null)

    if (completedTrades.length === 0) {
      return {
        totalTrades: sortedTrades.length,
        completedTrades: 0,
        totalPnL: 0,
        winningTrades: 0,
        losingTrades: 0,
        winRate: 0,
        avgWin: 0,
        avgLoss: 0,
        avgReturn: 0,
        bestTrade: 0,
        worstTrade: 0,
        totalFees: 0
      }
    }

    const totalPnL = completedTrades.reduce((sum, trade) => sum + (trade.profit_loss || 0), 0)
    const winningTrades = completedTrades.filter(trade => (trade.profit_loss || 0) > 0)
    const losingTrades = completedTrades.filter(trade => (trade.profit_loss || 0) < 0)
    const totalFees = completedTrades.reduce((sum, trade) => sum + (trade.fees || 0), 0)

    const avgWin = winningTrades.length > 0
      ? winningTrades.reduce((sum, trade) => sum + (trade.profit_loss || 0), 0) / winningTrades.length
      : 0

    const avgLoss = losingTrades.length > 0
      ? losingTrades.reduce((sum, trade) => sum + (trade.profit_loss || 0), 0) / losingTrades.length
      : 0

    const avgReturn = completedTrades.length > 0
      ? completedTrades.reduce((sum, trade) => sum + (trade.percentage_return || 0), 0) / completedTrades.length
      : 0

    const pnlValues = completedTrades.map(trade => trade.profit_loss || 0)
    const bestTrade = pnlValues.length > 0 ? Math.max(...pnlValues) : 0
    const worstTrade = pnlValues.length > 0 ? Math.min(...pnlValues) : 0



    return {
      totalTrades: sortedTrades.length,
      completedTrades: completedTrades.length,
      totalPnL,
      winningTrades: winningTrades.length,
      losingTrades: losingTrades.length,
      winRate: completedTrades.length > 0 ? (winningTrades.length / completedTrades.length) * 100 : 0,
      avgWin,
      avgLoss,
      avgReturn,
      bestTrade,
      worstTrade,
      totalFees
    }
  }

  const stats = calculateTradeStats()

  // Clear all filters
  const clearAllFilters = () => {
    setSearchTerm('')
    setDateRangeStart('')
    setDateRangeEnd('')
    setProfitLossFilter('all')
    setOptionTypeFilter('all')
    setDayOfWeekFilter('all')
    setMinContracts('')
    setMaxContracts('')
  }

  // Check if any filters are active
  const hasActiveFilters = searchTerm || dateRangeStart || dateRangeEnd ||
    profitLossFilter !== 'all' || optionTypeFilter !== 'all' ||
    dayOfWeekFilter !== 'all' || minContracts || maxContracts

  // Export trades to CSV
  const exportToCSV = () => {
    if (sortedTrades.length === 0) {
      alert('No trades to export')
      return
    }

    // Define CSV headers
    const headers = [
      'Entry Date/Time',
      'Exit Date/Time',
      'Ticker',
      'Trade Type',
      'Option Type',
      'Strike Price',
      'Expiration Date',
      'Contracts',
      'Entry Price',
      'Exit Price',
      'Fees',
      'Profit/Loss',
      'Day of Week',
      'Created At',
      'Updated At'
    ]

    // Convert trades to CSV rows
    const csvRows = sortedTrades.map(trade => {
      const entryDate = getNormalizedDate(trade.entry_datetime)
      const dayOfWeek = entryDate ? format(parseLocalDate(entryDate), 'EEEE') : ''

      return [
        formatDateTimeRaw(trade.entry_datetime),
        trade.exit_datetime ? formatDateTimeRaw(trade.exit_datetime) : '',
        trade.ticker,
        trade.trade_type,
        trade.option_type || '',
        trade.option_strike || '',
        trade.option_expiration || '',
        trade.contracts || '',
        trade.entry_price,
        trade.exit_price || '',
        trade.fees || '',
        trade.profit_loss || '',
        dayOfWeek,
        format(new Date(trade.created_at), 'yyyy-MM-dd HH:mm:ss'),
        format(new Date(trade.updated_at), 'yyyy-MM-dd HH:mm:ss')
      ]
    })

    // Combine headers and rows
    const csvContent = [headers, ...csvRows]
      .map(row => row.map(field => {
        // Escape fields that contain commas, quotes, or newlines
        const stringField = String(field)
        if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
          return `"${stringField.replace(/"/g, '""')}"`
        }
        return stringField
      }).join(','))
      .join('\n')

    // Create and download the file
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' })
    const link = document.createElement('a')

    if (link.download !== undefined) {
      const url = URL.createObjectURL(blob)
      link.setAttribute('href', url)

      // Generate filename with current date and filter info
      const now = new Date()
      const dateStr = format(now, 'yyyy-MM-dd')
      let filename = `trades-${dateStr}`

      if (searchTerm) filename += `-${searchTerm}`
      if (profitLossFilter !== 'all') filename += `-${profitLossFilter}`
      if (dayOfWeekFilter !== 'all') filename += `-${dayOfWeekFilter}`
      if (selectedDate) filename += `-${selectedDate}`

      link.setAttribute('download', `${filename}.csv`)
      link.style.visibility = 'hidden'
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
    }
  }

  return (
    <>
      <div className="w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg">
        {/* Header */}
        <div className="p-3 sm:p-4 lg:p-6 border-b border-gray-200 dark:border-gray-700">
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
          <h2 className="text-xl font-semibold text-gray-900 dark:text-white">
            Trade Log {selectedDate && `- ${format(new Date(selectedDate), 'MMM dd, yyyy')}`}
          </h2>
          
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500 w-4 h-4" />
            <input
              type="text"
              placeholder="Search by ticker..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-10 pr-4 py-2 border border-gray-300 dark:border-gray-600 rounded-md bg-white dark:bg-gray-700 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400 focus:outline-none focus:ring-2 focus:ring-blue-500 w-full sm:w-64"
            />
          </div>
        </div>

        {/* Sort Controls and Filter Toggle */}
        <div className="flex items-center justify-between mt-4">
          <div className="flex items-center space-x-4">
            <span className="text-sm text-gray-600 dark:text-gray-400">Sort by:</span>
            <select
              value={sortBy}
              onChange={(e) => setSortBy(e.target.value as 'entry_datetime' | 'ticker' | 'profit_loss' | 'percentage_return')}
              className="text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white focus:outline-none focus:ring-2 focus:ring-blue-500"
              title="Sort by field"
            >
              <option value="entry_datetime">Date</option>
              <option value="ticker">Ticker</option>
              <option value="profit_loss">P&L</option>
              <option value="percentage_return">% Return</option>
            </select>
            <button
              type="button"
              onClick={() => setSortOrder(sortOrder === 'asc' ? 'desc' : 'asc')}
              className="text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300"
              title={`Sort ${sortOrder === 'asc' ? 'descending' : 'ascending'}`}
            >
              {sortOrder === 'asc' ? '↑' : '↓'}
            </button>
          </div>

          <div className="flex items-center space-x-2">
            <button
              type="button"
              onClick={() => setShowFilters(!showFilters)}
              className="text-sm text-blue-600 hover:text-blue-800 flex items-center space-x-1"
            >
              <Filter className="w-4 h-4" />
              <span>{showFilters ? 'Hide Filters' : 'Show Filters'}</span>
            </button>
            {hasActiveFilters && (
              <button
                type="button"
                onClick={clearAllFilters}
                className="text-sm text-red-600 hover:text-red-800"
              >
                Clear Filters
              </button>
            )}
            {sortedTrades.length > 0 && (
              <button
                type="button"
                onClick={exportToCSV}
                className="text-sm text-black-600 hover:text-black-800 flex items-center space-x-1"
                title="Export filtered trades to CSV"
              >
                <Download className="w-4 h-4" />
                <span>Export CSV</span>
              </button>
            )}
          </div>
        </div>

        {/* Advanced Filters */}
        {showFilters && (
          <div className="mt-4 p-4 bg-gray-50 dark:bg-gray-700 rounded-lg">
            <h4 className="text-sm font-medium text-gray-900 dark:text-white mb-3">Advanced Filters</h4>

            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
              {/* Date Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Date Range</label>
                <div className="flex space-x-2">
                  <input
                    type="date"
                    value={dateRangeStart}
                    onChange={(e) => setDateRangeStart(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                    placeholder="Start"
                  />
                  <input
                    type="date"
                    value={dateRangeEnd}
                    onChange={(e) => setDateRangeEnd(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                    placeholder="End"
                  />
                </div>
              </div>

              {/* Profit/Loss Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Trade Result</label>
                <select
                  value={profitLossFilter}
                  onChange={(e) => setProfitLossFilter(e.target.value as 'all' | 'winners' | 'losers' | 'open')}
                  className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-full bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                  title="Filter by trade result"
                >
                  <option value="all">All Trades</option>
                  <option value="winners">Winners Only</option>
                  <option value="losers">Losers Only</option>
                  <option value="open">Open Positions</option>
                </select>
              </div>

              {/* Option Type Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Option Type</label>
                <select
                  value={optionTypeFilter}
                  onChange={(e) => setOptionTypeFilter(e.target.value as 'all' | 'Call' | 'Put')}
                  className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-full bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                  title="Filter by option type"
                >
                  <option value="all">All Types</option>
                  <option value="Call">Calls Only</option>
                  <option value="Put">Puts Only</option>
                </select>
              </div>

              {/* Day of Week Filter */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Day of Week</label>
                <select
                  value={dayOfWeekFilter}
                  onChange={(e) => setDayOfWeekFilter(e.target.value as 'all' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday')}
                  className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 w-full bg-white dark:bg-gray-600 text-gray-900 dark:text-white"
                  title="Filter by day of week"
                >
                  <option value="all">All Days</option>
                  <option value="Monday">Monday</option>
                  <option value="Tuesday">Tuesday</option>
                  <option value="Wednesday">Wednesday</option>
                  <option value="Thursday">Thursday</option>
                  <option value="Friday">Friday</option>
                  <option value="Saturday">Saturday</option>
                  <option value="Sunday">Sunday</option>
                </select>
              </div>

              {/* Contracts Range */}
              <div>
                <label className="block text-xs font-medium text-gray-700 dark:text-gray-300 mb-1">Contracts</label>
                <div className="flex space-x-2">
                  <input
                    type="number"
                    min="1"
                    value={minContracts}
                    onChange={(e) => setMinContracts(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Min"
                  />
                  <input
                    type="number"
                    min="1"
                    value={maxContracts}
                    onChange={(e) => setMaxContracts(e.target.value)}
                    className="text-xs border border-gray-300 dark:border-gray-600 rounded px-2 py-1 flex-1 bg-white dark:bg-gray-600 text-gray-900 dark:text-white placeholder-gray-500 dark:placeholder-gray-400"
                    placeholder="Max"
                  />
                </div>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Trade Statistics Cards */}
      {stats.totalTrades > 0 && (
        <div className="mb-6 mx-4">
          <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-4 text-center">
            Statistics
            {searchTerm && ` for "${searchTerm}"`}
            {selectedDate && ` on ${format(parseLocalDate(selectedDate), 'MMM dd, yyyy')}`}
            {profitLossFilter !== 'all' && ` (${profitLossFilter})`}
            {optionTypeFilter !== 'all' && ` (${optionTypeFilter}s)`}
            {dayOfWeekFilter !== 'all' && ` (${dayOfWeekFilter}s)`}
            {(dateRangeStart || dateRangeEnd) && ` (filtered)`}
          </h3>

          <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-3 lg:grid-cols-6 xl:grid-cols-6 gap-4">
            {/* Total Trades */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-2xl font-bold text-gray-900 dark:text-white">{stats.totalTrades}</div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Trades</div>
              {stats.completedTrades !== stats.totalTrades && (
                <div className="text-xs text-gray-400 dark:text-gray-500">
                  {stats.completedTrades} completed
                </div>
              )}
            </div>

            {/* Total P&L */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.totalPnL)}`}>
                {stats.totalPnL >= 0 ? '+$' : '-$'}{Math.abs(stats.totalPnL).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total P&L</div>
            </div>

            {/* Win Rate */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${stats.winRate >= 50 ? 'text-green-600 dark:text-green-400' : 'text-red-600 dark:text-red-400'}`}>
                {stats.winRate.toFixed(1)}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Win Rate</div>
              <div className="text-xs text-gray-400 dark:text-gray-500">
                {stats.winningTrades}W / {stats.losingTrades}L
              </div>
            </div>

            {/* Average Return */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.avgReturn)}`}>
                {stats.avgReturn >= 0 ? '+' : ''}{stats.avgReturn.toFixed(2)}%
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg Return</div>
            </div>

            {/* Best Trade */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.bestTrade)}`}>
                {stats.bestTrade >= 0 ? '+$' : '-$'}{Math.abs(stats.bestTrade).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Best Trade</div>
            </div>

            {/* Worst Trade */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className={`text-2xl font-bold ${getProfitLossColor(stats.worstTrade)}`}>
                {stats.worstTrade >= 0 ? '+$' : '-$'}{Math.abs(stats.worstTrade).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Worst Trade</div>
            </div>






          </div>

          {/* Additional Stats Row */}
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4 mt-4">
            {/* Average Win */}
            <div className="bg-green-50 dark:bg-green-900/20 rounded-lg p-4 text-center">
              <div className="text-xl font-bold text-green-600 dark:text-green-400">
                +${stats.avgWin.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg Win</div>
            </div>

            {/* Average Loss */}
            <div className="bg-red-50 dark:bg-red-900/20 rounded-lg p-4 text-center">
              <div className="text-xl font-bold text-red-600 dark:text-red-400">
                {stats.avgLoss >= 0 ? '+$' : '-$'}{Math.abs(stats.avgLoss).toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Avg Loss</div>
            </div>

            {/* Total Fees */}
            <div className="bg-gray-50 dark:bg-gray-700 rounded-lg p-4 text-center">
              <div className="text-xl font-bold text-gray-900 dark:text-white">
                ${stats.totalFees.toFixed(2)}
              </div>
              <div className="text-sm text-gray-500 dark:text-gray-400">Total Fees</div>
            </div>
          </div>
        </div>
      )}

      {/* Trade Table */}
      <div className="w-full overflow-x-auto">
        <table className="min-w-full w-full">
          <thead className="bg-gray-50 dark:bg-gray-700">
            <tr>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Status
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                <span className="hidden sm:inline">Entry Date/Time</span>
                <span className="sm:hidden">Entry</span>
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Ticker
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                <span className="hidden sm:inline">Type/Details</span>
                <span className="sm:hidden">Type</span>
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                <span className="hidden sm:inline">Quantity</span>
                <span className="sm:hidden">Qty</span>
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                <span className="hidden sm:inline">Entry Price</span>
                <span className="sm:hidden">Entry</span>
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                <span className="hidden sm:inline">Exit Price</span>
                <span className="sm:hidden">Exit</span>
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                P&L
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                <span className="hidden sm:inline">% Return</span>
                <span className="sm:hidden">%</span>
              </th>
              <th className="px-2 sm:px-4 lg:px-6 py-2 sm:py-3 text-left text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white dark:bg-gray-800 divide-y divide-gray-200 dark:divide-gray-700">
            {loading ? (
              <tr>
                <td colSpan={10} className="px-2 sm:px-4 lg:px-6 py-4 sm:py-6 text-center text-gray-500 dark:text-gray-400">
                  Loading trades...
                </td>
              </tr>
            ) : sortedTrades.length === 0 ? (
              <tr>
                <td colSpan={10} className="px-2 sm:px-4 lg:px-6 py-4 sm:py-6 text-center text-gray-500 dark:text-gray-400">
                  {isSupabaseConfigured && !user
                    ? 'Sign in to view your trades'
                    : 'No trades found'
                  }
                </td>
              </tr>
            ) : (
              paginatedTrades.map((trade) => (
                <tr
                  key={trade.id}
                  className="hover:bg-gray-50 dark:hover:bg-gray-700 cursor-pointer"
                  onClick={() => onViewTrade?.(trade)}
                  tabIndex={0}
                  onKeyDown={(e) => {
                    if (e.key === 'Enter' || e.key === ' ') {
                      e.preventDefault()
                      onViewTrade?.(trade)
                    }
                  }}
                >
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-sm font-medium">
                    <span className={`inline-flex px-1 sm:px-2 py-1 text-xs font-semibold rounded-full ${
                      trade.status === 'Open'
                        ? 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200'
                        : 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200'
                    }`}>
                      <span className="hidden sm:inline">{trade.status}</span>
                      <span className="sm:hidden">{trade.status === 'Open' ? 'O' : 'C'}</span>
                    </span>
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    <span className="hidden sm:inline">{formatDateTime(trade.entry_datetime)}</span>
                    <span className="sm:hidden">{formatShortDateDisplay(trade.entry_datetime)}</span>
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-sm font-medium text-gray-900 dark:text-white">
                    {trade.ticker}
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500 dark:text-gray-400">
                    {trade.trade_type === 'option' ? (
                      <div>
                        <div className="text-xs font-medium text-blue-600 dark:text-blue-400 mb-1">
                          Option
                        </div>
                        <div>
                          ${trade.option_strike}
                          {trade.option_type && (
                            <span className="ml-1 text-xs font-medium text-gray-600 dark:text-gray-400">
                              {trade.option_type === 'Call' ? 'C' : 'P'}
                            </span>
                          )}
                        </div>
                        {trade.option_expiration && (
                          <div className="text-xs">
                            {format(parseLocalDate(trade.option_expiration), 'MM/dd/yy')}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div>
                        <div className="text-xs font-medium text-gray-600 dark:text-gray-400">
                          {trade.trade_type}
                        </div>
                      </div>
                    )}
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {trade.contracts}
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {formatCurrency(trade.entry_price)}
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-900 dark:text-gray-300">
                    {formatCurrency(trade.exit_price)}
                  </td>
                  <td className={`px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium ${getProfitLossColor(trade.profit_loss)}`}>
                    {formatCurrency(trade.profit_loss)}
                  </td>
                  <td className={`px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm font-medium ${getProfitLossColor(trade.percentage_return)}`}>
                    {trade.percentage_return !== null && trade.percentage_return !== undefined
                      ? `${trade.percentage_return >= 0 ? '+' : ''}${trade.percentage_return.toFixed(2)}%`
                      : '-'
                    }
                  </td>
                  <td className="px-2 sm:px-4 lg:px-6 py-2 sm:py-4 whitespace-nowrap text-xs sm:text-sm text-gray-500">
                    <div className="flex space-x-1 sm:space-x-2">
                      <button
                        type="button"
                        onClick={(e) => { e.stopPropagation(); onEditTrade(trade) }}
                        className="text-blue-600 hover:text-blue-800"
                        title="Edit trade"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                      <button
                        type="button"
                        onClick={(e) => { e.stopPropagation(); handleDelete(trade.id) }}
                        className="text-red-600 hover:text-red-800"
                        title="Delete trade"
                      >
                        <Trash2 className="w-3 h-3 sm:w-4 sm:h-4" />
                      </button>
                    </div>
                  </td>
                </tr>
              ))
            )}
          </tbody>
        </table>
      </div>

      {/* Pagination Controls */}
      {sortedTrades.length > 0 && (
        <div className="px-3 sm:px-4 lg:px-6 py-3 sm:py-4 border-t border-gray-200 dark:border-gray-700">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4">
            {/* Page Size Selector */}
            <div className="flex items-center space-x-2">
              <span className="text-xs sm:text-sm text-gray-700 dark:text-gray-300">
                <span className="hidden sm:inline">Show:</span>
                <span className="sm:hidden">Per page:</span>
              </span>
              <select
                value={pageSize}
                onChange={(e) => setPageSize(Number(e.target.value))}
                className="text-xs sm:text-sm border border-gray-300 dark:border-gray-600 rounded px-2 py-1 bg-white dark:bg-gray-700 text-gray-900 dark:text-white"
                aria-label="Select number of trades per page"
              >
                <option value={10}>10</option>
                <option value={25}>25</option>
                <option value={50}>50</option>
                <option value={100}>100</option>
                <option value={totalTrades}>All ({totalTrades})</option>
              </select>
              <span className="hidden sm:inline text-xs sm:text-sm text-gray-700 dark:text-gray-300">trades per page</span>
            </div>

            {/* Pagination Info and Controls */}
            <div className="flex flex-col sm:flex-row items-center space-y-2 sm:space-y-0 sm:space-x-4">
              {/* Page Info */}
              <span className="text-xs sm:text-sm text-gray-700 dark:text-gray-300">
                <span className="hidden sm:inline">Showing {startIndex + 1}-{Math.min(endIndex, totalTrades)} of {totalTrades} trades</span>
                <span className="sm:hidden">{startIndex + 1}-{Math.min(endIndex, totalTrades)} of {totalTrades}</span>
              </span>

              {/* Page Navigation */}
              {totalPages > 1 && (
                <div className="flex items-center space-x-1">
                  {/* Previous Button */}
                  <button
                    type="button"
                    onClick={() => setCurrentPage(prev => Math.max(prev - 1, 1))}
                    disabled={currentPage === 1}
                    className="px-2 sm:px-3 py-1 text-xs sm:text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  >
                    <span className="hidden sm:inline">Previous</span>
                    <span className="sm:hidden">Prev</span>
                  </button>

                  {/* Page Numbers */}
                  <div className="flex items-center space-x-1">
                    {Array.from({ length: Math.min(totalPages <= 3 ? totalPages : 3, totalPages) }, (_, i) => {
                      let pageNum
                      if (totalPages <= 3) {
                        pageNum = i + 1
                      } else if (currentPage <= 2) {
                        pageNum = i + 1
                      } else if (currentPage >= totalPages - 1) {
                        pageNum = totalPages - 2 + i
                      } else {
                        pageNum = currentPage - 1 + i
                      }

                      return (
                        <button
                          type="button"
                          key={pageNum}
                          onClick={() => setCurrentPage(pageNum)}
                          className={`px-2 sm:px-3 py-1 text-xs sm:text-sm border rounded ${
                            currentPage === pageNum
                              ? 'bg-blue-600 text-white border-blue-600'
                              : 'border-gray-300 dark:border-gray-600 hover:bg-gray-50 dark:hover:bg-gray-700 bg-white dark:bg-gray-800 text-gray-900 dark:text-white'
                          }`}
                        >
                          {pageNum}
                        </button>
                      )
                    })}
                  </div>

                  {/* Next Button */}
                  <button
                    type="button"
                    onClick={() => setCurrentPage(prev => Math.min(prev + 1, totalPages))}
                    disabled={currentPage === totalPages}
                    className="px-2 sm:px-3 py-1 text-xs sm:text-sm border border-gray-300 dark:border-gray-600 rounded hover:bg-gray-50 dark:hover:bg-gray-700 disabled:opacity-50 disabled:cursor-not-allowed bg-white dark:bg-gray-800 text-gray-900 dark:text-white"
                  >
                    <span className="hidden sm:inline">Next</span>
                    <span className="sm:hidden">Next</span>
                  </button>
                </div>
              )}
            </div>
          </div>
        </div>
      )}
    </div>
      {undoState && (
        <div className="fixed bottom-4 left-1/2 z-50 w-full max-w-xs -translate-x-1/2 sm:max-w-md">
          <div className="flex items-center justify-between space-x-3 rounded-lg bg-gray-900 px-4 py-3 shadow-lg dark:bg-gray-700">
            <span className="text-sm text-white">
              {undoState.type === 'delete'
                ? `Trade ${undoState.tradeBefore.ticker} deleted`
                : `Trade ${undoState.optimisticTrade.ticker} updated`}
            </span>
            <button
              type="button"
              onClick={handleUndo}
              className="rounded-md bg-white px-3 py-1 text-sm font-semibold text-gray-900 transition-colors hover:bg-gray-100"
            >
              Undo
            </button>
          </div>
        </div>
      )}
    </>
  )
}
