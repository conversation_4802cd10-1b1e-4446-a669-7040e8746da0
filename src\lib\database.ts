import { supabase, Trade, UserPreferences, TradeSetup, SupportType, isSupabaseConfigured } from './supabase'

// Type for creating new trades
export type NewTrade = {
  trade_type: 'option'
  status: 'Open' | 'Closed'
  entry_datetime: string
  exit_datetime: string | null
  ticker: string
  option_type: 'Call' | 'Put'
  option_strike: number
  option_expiration: string
  contracts: number
  entry_price: number
  exit_price: number | null
  mae_price?: number | null
  mfe_price?: number | null
  trade_setup?: string | null
  support_type?: string | null
  price_to_vwap_position?: 'Above' | 'At' | 'Below' | null
}

// Calculate percentage return for option trades
export function calculatePercentageReturn(
  profitLoss: number,
  entryPrice: number,
  contracts: number
): number {
  if (!profitLoss || !entryPrice || !contracts || entryPrice <= 0 || contracts <= 0) {
    return 0
  }

  // For options: investment is (entry price * 100) * number of contracts
  const totalInvestment = (entryPrice * 100) * contracts

  const percentageReturn = (profitLoss / totalInvestment) * 100

  // Round to 4 decimal places for precision
  return Math.round(percentageReturn * 10000) / 10000
}

// Mock data for when Supabase is not configured (demo mode only)
const today = new Date().toISOString().split('T')[0]
const yesterday = new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString().split('T')[0]
const mockUserId = 'demo-user-id'

const mockTrades: Trade[] = [
  {
    id: '1',
    user_id: mockUserId,
    status: 'Closed',
    entry_datetime: `${today}T09:30:00-05:00`,
    exit_datetime: `${today}T10:15:00-05:00`,
    ticker: 'SPY',
    trade_type: 'option',
    option_type: 'Call',
    option_strike: 480.00,
    option_expiration: `${today}`,
    contracts: 2,
    entry_price: 2.50,
    exit_price: 3.20,
    fees: 2.60, // 2 contracts * $1.30
    profit_loss: 137.40, // (3.20 - 2.50) * 2 * 100 - 2.60 = 140.00 - 2.60 = 137.40
    percentage_return: calculatePercentageReturn(137.40, 2.50, 2), // 27.48%
    mae_price: null,
    mfe_price: null,
    trade_setup: null,
    support_type: null,
    price_to_vwap_position: null,
    created_at: `${today}T09:30:00-05:00`,
    updated_at: `${today}T09:30:00-05:00`
  },
  {
    id: '2',
    user_id: mockUserId,
    status: 'Closed',
    entry_datetime: `${yesterday}T11:00:00-05:00`,
    exit_datetime: `${yesterday}T11:45:00-05:00`,
    ticker: 'QQQ',
    trade_type: 'option',
    option_type: 'Put',
    option_strike: 390.00,
    option_expiration: `${yesterday}`,
    contracts: 1,
    entry_price: 1.80,
    exit_price: 1.45,
    fees: 1.30, // 1 contract * $1.30
    profit_loss: -36.30, // (1.45 - 1.80) * 1 * 100 - 1.30 = -35.00 - 1.30 = -36.30
    percentage_return: calculatePercentageReturn(-36.30, 1.80, 1), // -20.17%
    mae_price: null,
    mfe_price: null,
    trade_setup: null,
    support_type: null,
    price_to_vwap_position: null,
    created_at: `${yesterday}T11:00:00-05:00`,
    updated_at: `${yesterday}T11:00:00-05:00`
  }
]

// Function to calculate daily stats from mock trades
function calculateMockDailyStats(startDate?: string, endDate?: string) {
  const statsMap = new Map()

  mockTrades.forEach(trade => {
    if (!trade.exit_price || !trade.profit_loss) return // Skip open positions

    const tradeDate = trade.entry_datetime.split('T')[0] // Get date part

    // Apply date filters
    if (startDate && tradeDate < startDate) return
    if (endDate && tradeDate > endDate) return

    if (!statsMap.has(tradeDate)) {
      statsMap.set(tradeDate, {
        date: tradeDate,
        total_profit_loss: 0,
        total_r_return: 0,
        trade_count: 0,
        winning_trades: 0,
        losing_trades: 0
      })
    }

    const stats = statsMap.get(tradeDate)
    stats.trade_count += 1
    stats.total_profit_loss += trade.profit_loss

    // Add R-return if available (only for stock trades)
    // Not applicable for option trades in this demo; keep total_r_return as 0

    if (trade.profit_loss > 0) {
      stats.winning_trades += 1
    } else if (trade.profit_loss < 0) {
      stats.losing_trades += 1
    }
  })

  return Array.from(statsMap.values()).sort((a, b) => b.date.localeCompare(a.date))
}

// Trade operations
export async function createTrade(trade: NewTrade) {
  if (!isSupabaseConfigured) {
    // Ensure numeric values are properly converted
    const entry_price = Number(trade.entry_price)
    const exit_price = trade.exit_price ? Number(trade.exit_price) : null
    const option_strike = Number(trade.option_strike)
    const contracts = Number(trade.contracts)

    // Calculate fees: $1.30 per contract
    const fees = contracts * 1.30

    let profit_loss: number | null = null
    let percentage_return: number | null = null

    // Calculate profit/loss including fees (multiply by 100 for options contracts)
    if (exit_price) {
      profit_loss = (exit_price - entry_price) * contracts * 100 - fees
      percentage_return = calculatePercentageReturn(profit_loss, entry_price, contracts)
    }

    // Return mock success for demo purposes
    const newTrade: Trade = {
      ...trade,
      id: Math.random().toString(36).substr(2, 9),
      user_id: mockUserId,
      option_type: trade.option_type,
      option_strike,
      option_expiration: trade.option_expiration,
      contracts,
      mae_price: trade.mae_price ?? null,
      mfe_price: trade.mfe_price ?? null,
      trade_setup: trade.trade_setup ?? null,
      support_type: trade.support_type ?? null,
      price_to_vwap_position: trade.price_to_vwap_position ?? null,
      entry_price,
      exit_price,
      fees,
      profit_loss,
      percentage_return,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    }
    mockTrades.unshift(newTrade)
    return newTrade
  }

  // Get current user
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    throw new Error('User must be authenticated to create trades')
  }

  // Calculate percentage return for option trades
  let percentage_return: number | null = null

  if (trade.exit_price && trade.contracts) {
    const profit_loss = ((trade.exit_price - trade.entry_price) * trade.contracts * 100) - (trade.contracts * 1.30)
    percentage_return = calculatePercentageReturn(profit_loss, trade.entry_price, trade.contracts)
  }

  const { data, error } = await supabase
    .from('trades')
    .insert([{ ...trade, user_id: user.id, percentage_return }])
    .select()
    .single()

  if (error) {
    console.error('Error creating trade:', error)
    throw new Error('Failed to create trade. Please try again.')
  }

  return data
}

export async function updateTrade(id: string, updates: Partial<Trade>) {
  if (!isSupabaseConfigured) {
    const tradeIndex = mockTrades.findIndex(trade => trade.id === id)
    if (tradeIndex === -1) {
      throw new Error('Trade not found')
    }

    const updatedTrade = {
      ...mockTrades[tradeIndex],
      ...updates,
      updated_at: new Date().toISOString()
    }

    // Ensure numeric values are properly converted
    updatedTrade.entry_price = Number(updatedTrade.entry_price)
    if (updatedTrade.exit_price !== null) {
      updatedTrade.exit_price = Number(updatedTrade.exit_price)
    }
    updatedTrade.option_strike = Number(updatedTrade.option_strike)
    updatedTrade.contracts = Number(updatedTrade.contracts)

    // Recalculate fees, profit/loss, and returns for option trades
    updatedTrade.fees = updatedTrade.contracts * 1.30

    if (updatedTrade.exit_price && updatedTrade.entry_price) {
      const grossPL = (updatedTrade.exit_price - updatedTrade.entry_price) * updatedTrade.contracts * 100
      updatedTrade.profit_loss = grossPL - updatedTrade.fees
      updatedTrade.percentage_return = calculatePercentageReturn(
        updatedTrade.profit_loss,
        updatedTrade.entry_price,
        updatedTrade.contracts
      )
    } else {
      updatedTrade.profit_loss = null
      updatedTrade.percentage_return = null
    }

    mockTrades[tradeIndex] = updatedTrade
    return updatedTrade
  }

  // Get the current trade to calculate percentage return
  const { data: currentTrade } = await supabase
    .from('trades')
    .select('*')
    .eq('id', id)
    .single()

  if (currentTrade) {
    // Merge updates with current trade data
    const mergedTrade = { ...currentTrade, ...updates }

    // Calculate percentage return for option trades
    if (mergedTrade.exit_price && mergedTrade.entry_price && mergedTrade.contracts) {
      const profit_loss = ((mergedTrade.exit_price - mergedTrade.entry_price) * mergedTrade.contracts * 100) - (mergedTrade.contracts * 1.30)
      updates.percentage_return = calculatePercentageReturn(
        profit_loss,
        mergedTrade.entry_price,
        mergedTrade.contracts
      )
    } else {
      updates.percentage_return = null
    }
  }

  const { data, error } = await supabase
    .from('trades')
    .update(updates)
    .eq('id', id)
    .select()
    .single()

  if (error) {
    console.error('Error updating trade:', error)
    throw new Error('Failed to update trade. Please try again.')
  }

  return data
}

export async function deleteTrade(id: string) {
  if (!isSupabaseConfigured) {
    const index = mockTrades.findIndex(trade => trade.id === id)
    if (index > -1) {
      mockTrades.splice(index, 1)
    }
    return
  }

  const { error } = await supabase
    .from('trades')
    .delete()
    .eq('id', id)

  if (error) {
    console.error('Error deleting trade:', error)
    throw new Error('Failed to delete trade. Please try again.')
  }
}

// Trade Setup operations
export async function getTradeSetups(): Promise<TradeSetup[]> {
  if (!isSupabaseConfigured) {
    // Return empty array for demo mode
    return []
  }

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return []
  }

  const { data, error } = await supabase
    .from('trade_setups')
    .select('*')
    .eq('user_id', user.id)
    .order('name')

  if (error) {
    console.error('Error fetching trade setups:', error)
    return []
  }

  return data || []
}

export async function createTradeSetup(name: string): Promise<TradeSetup | null> {
  if (!isSupabaseConfigured) {
    return null
  }

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return null
  }

  const { data, error } = await supabase
    .from('trade_setups')
    .insert([{ user_id: user.id, name }])
    .select()
    .single()

  if (error) {
    console.error('Error creating trade setup:', error)
    return null
  }

  return data
}

export async function deleteTradeSetup(id: string): Promise<boolean> {
  if (!isSupabaseConfigured) {
    return false
  }

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return false
  }

  const { error } = await supabase
    .from('trade_setups')
    .delete()
    .eq('id', id)
    .eq('user_id', user.id)

  if (error) {
    console.error('Error deleting trade setup:', error)
    return false
  }

  return true
}

// Support Type operations
export async function getSupportTypes(): Promise<SupportType[]> {
  if (!isSupabaseConfigured) {
    // Return empty array for demo mode
    return []
  }

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return []
  }

  const { data, error } = await supabase
    .from('support_types')
    .select('*')
    .eq('user_id', user.id)
    .order('name')

  if (error) {
    console.error('Error fetching support types:', error)
    return []
  }

  return data || []
}

export async function createSupportType(name: string): Promise<SupportType | null> {
  if (!isSupabaseConfigured) {
    return null
  }

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return null
  }

  const { data, error } = await supabase
    .from('support_types')
    .insert([{ user_id: user.id, name }])
    .select()
    .single()

  if (error) {
    console.error('Error creating support type:', error)
    return null
  }

  return data
}

export async function deleteSupportType(id: string): Promise<boolean> {
  if (!isSupabaseConfigured) {
    return false
  }

  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return false
  }

  const { error } = await supabase
    .from('support_types')
    .delete()
    .eq('id', id)
    .eq('user_id', user.id)

  if (error) {
    console.error('Error deleting support type:', error)
    return false
  }

  return true
}

export async function getTrades(limit = 100, offset = 0) {
  if (!isSupabaseConfigured) {
    return mockTrades.slice(offset, offset + limit)
  }

  // Get current user
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return [] // Return empty array if not authenticated
  }

  const { data, error } = await supabase
    .from('trades')
    .select('*')
    .eq('user_id', user.id)
    .order('entry_datetime', { ascending: false })
    .range(offset, offset + limit - 1)

  if (error) {
    console.error('Error fetching trades:', error)
    throw new Error('Failed to fetch trades. Please try again.')
  }

  return data
}

export async function searchTrades(searchTerm: string) {
  if (!isSupabaseConfigured) {
    return mockTrades.filter(trade =>
      trade.ticker.toLowerCase().includes(searchTerm.toLowerCase())
    )
  }

  // Get current user
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return [] // Return empty array if not authenticated
  }

  // Sanitize search term to prevent injection
  const sanitizedSearchTerm = searchTerm
    .replace(/[%_\\]/g, '\\$&') // Escape SQL wildcards
    .trim()
    .substring(0, 50) // Limit length
    .toUpperCase()

  if (!sanitizedSearchTerm) {
    return [] // Return empty array for empty search
  }

  const { data, error } = await supabase
    .from('trades')
    .select('*')
    .eq('user_id', user.id)
    .ilike('ticker', `%${sanitizedSearchTerm}%`)
    .order('entry_datetime', { ascending: false })

  if (error) {
    console.error('Error searching trades:', error)
    throw new Error('Failed to search trades. Please try again.')
  }

  return data
}

// Daily statistics
export async function getDailyStats(startDate?: string, endDate?: string) {
  if (!isSupabaseConfigured) {
    // In demo mode, always return mock data since we can't check auth
    return calculateMockDailyStats(startDate, endDate)
  }

  // Get current user
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return [] // Return empty array if not authenticated
  }

  // Get trades for the date range and calculate daily stats
  let query = supabase
    .from('trades')
    .select('*')
    .eq('user_id', user.id)
    .not('exit_price', 'is', null) // Only completed trades

  if (startDate) {
    query = query.gte('entry_datetime', `${startDate}T00:00:00`)
  }

  if (endDate) {
    query = query.lte('entry_datetime', `${endDate}T23:59:59`)
  }

  const { data: trades, error } = await query

  if (error) {
    console.error('Error fetching trades for daily stats:', error)
    throw new Error('Failed to fetch daily statistics. Please try again.')
  }

  // Calculate daily stats from trades
  const statsMap = new Map()

  trades?.forEach(trade => {
    if (!trade.profit_loss) return // Skip trades without P&L

    const tradeDate = trade.entry_datetime.split('T')[0] // Get date part

    if (!statsMap.has(tradeDate)) {
      statsMap.set(tradeDate, {
        trade_date: tradeDate,
        total_profit_loss: 0,
        total_r_return: 0,
        trade_count: 0,
        winning_trades: 0,
        losing_trades: 0
      })
    }

    const stats = statsMap.get(tradeDate)
    stats.trade_count += 1
    stats.total_profit_loss += trade.profit_loss

    // Add R-return if available (only for stock trades)
    // Not applicable for option trades in this demo; keep total_r_return as 0

    if (trade.profit_loss > 0) {
      stats.winning_trades += 1
    } else if (trade.profit_loss < 0) {
      stats.losing_trades += 1
    }
  })

  // Convert map to array and format for DailyStats interface
  return Array.from(statsMap.values()).map(stat => ({
    date: stat.trade_date,
    total_profit_loss: stat.total_profit_loss,
    total_r_return: stat.total_r_return,
    trade_count: stat.trade_count,
    winning_trades: stat.winning_trades,
    losing_trades: stat.losing_trades
  }))
}

export async function getTradesForDate(date: string) {
  const { data, error } = await supabase
    .from('trades')
    .select('*')
    .gte('entry_datetime', `${date}T00:00:00`)
    .lt('entry_datetime', `${date}T23:59:59`)
    .order('entry_datetime', { ascending: true })

  if (error) {
    console.error('Error fetching trades for date:', error)
    throw new Error('Failed to fetch trades for selected date. Please try again.')
  }

  return data
}

// Calendar data
export async function getCalendarData(year: number, month: number) {
  const startDate = new Date(year, month - 1, 1).toISOString().split('T')[0]
  const endDate = new Date(year, month, 0).toISOString().split('T')[0]

  return getDailyStats(startDate, endDate)
}

// User Preferences operations
export async function getUserPreferences(): Promise<UserPreferences | null> {
  if (!isSupabaseConfigured) {
    // In demo mode, return null (will fall back to localStorage)
    return null
  }

  // Get current user
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return null
  }

  const { data, error } = await supabase
    .from('user_preferences')
    .select('*')
    .eq('user_id', user.id)
    .single()

  if (error) {
    if (error.code === 'PGRST116') {
      // No preferences found, return null (will create default)
      return null
    }
    console.error('Error fetching user preferences:', error)
    return null
  }

  return data
}

export async function saveUserPreferences(theme: 'light' | 'dark'): Promise<UserPreferences | null> {
  if (!isSupabaseConfigured) {
    // In demo mode, return null (will fall back to localStorage)
    return null
  }

  // Get current user
  const { data: { user } } = await supabase.auth.getUser()
  if (!user) {
    return null
  }

  // Try to update existing preferences first
  const { data: updateData, error: updateError } = await supabase
    .from('user_preferences')
    .update({ theme })
    .eq('user_id', user.id)
    .select()
    .single()

  if (updateError) {
    if (updateError.code === 'PGRST116') {
      // No existing preferences, create new ones
      const { data: insertData, error: insertError } = await supabase
        .from('user_preferences')
        .insert([{ user_id: user.id, theme }])
        .select()
        .single()

      if (insertError) {
        console.error('Error creating user preferences:', insertError)
        return null
      }

      return insertData
    } else {
      console.error('Error updating user preferences:', updateError)
      return null
    }
  }

  return updateData
}
