'use client'

/**
 * Ensure datetime-local values remain unchanged when sent to the API.
 * Trims whitespace, keeps existing seconds if present, and returns null when empty.
 */
export function normalizeDateTimeLocal(value: string | null | undefined): string | null {
  if (!value) return null
  const trimmed = value.trim()
  if (!trimmed) return null

  const parts = trimmed.split('T')
  if (parts.length !== 2) {
    return trimmed
  }

  const [datePart, timePart] = parts
  if (!datePart || !timePart) {
    return trimmed
  }

  // Allow HH:mm or HH:mm:ss from datetime-local inputs.
  if (/^\d{2}:\d{2}$/.test(timePart)) {
    return `${datePart}T${timePart}`
  }
  if (/^\d{2}:\d{2}:\d{2}$/.test(timePart)) {
    return trimmed
  }

  // When the browser included seconds plus fractions, trim to HH:mm:ss.
  const secondsMatch = timePart.match(/^\d{2}:\d{2}:\d{2}/)
  if (secondsMatch) {
    return `${datePart}T${secondsMatch[0]}`
  }

  return trimmed
}

/**
 * Prepare stored timestamps for datetime-local inputs.
 * Returns the leading YYYY-MM-DDTHH:mm segment when present, otherwise falls back to Date parsing.
 */
export function formatDateTimeLocalField(value: string | null | undefined): string {
  if (!value) return ''
  const trimmed = value.trim()
  if (!trimmed) return ''

  const match = trimmed.match(/^\d{4}-\d{2}-\d{2}T\d{2}:\d{2}/)
  if (match && match[0]) {
    return match[0]
  }

  const date = new Date(trimmed)
  if (Number.isNaN(date.getTime())) {
    return ''
  }

  const year = date.getFullYear()
  const month = String(date.getMonth() + 1).padStart(2, '0')
  const day = String(date.getDate()).padStart(2, '0')
  const hours = String(date.getHours()).padStart(2, '0')
  const minutes = String(date.getMinutes()).padStart(2, '0')
  return `${year}-${month}-${day}T${hours}:${minutes}`
}
