'use client'

import type { Trade } from '@/lib/supabase'

export type Candle = {
  time: number // unix seconds
  open: number
  high: number
  low: number
  close: number
}

// kept for potential future use

export function getETDateString(date: Date) {
  // Format a Date into YYYY-MM-DD in US/Eastern
  const fmt = new Intl.DateTimeFormat('en-CA', { timeZone: 'America/New_York', year: 'numeric', month: '2-digit', day: '2-digit' })
  // en-CA returns YYYY-MM-DD by default with those options
  return fmt.format(date)
}

export async function fetchCandlesForTrade(
  trade: Trade,
  timeframe: '1m' | '5m' | '15m' = '5m'
): Promise<Candle[]> {
  // Request the full regular trading session for the ET date of the entry
  const entry = new Date(trade.entry_datetime)
  const etDate = getETDateString(entry)
  const symbol = trade.ticker.trim().toUpperCase()
  const params = new URLSearchParams({ symbol, date: etDate, timeframe })

  const res = await fetch(`/api/market-data?${params.toString()}`)
  if (!res.ok) {
    const text = await res.text()
    throw new Error(text || `Failed to fetch market data (${res.status})`)
  }
  const data = (await res.json()) as { candles: Candle[] }
  return data.candles || []
}

