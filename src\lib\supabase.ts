import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://placeholder.supabase.co'
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'placeholder-key'

// Check if we have valid Supabase configuration
const isSupabaseConfigured =
  supabaseUrl !== 'https://placeholder.supabase.co' &&
  supabaseAnonKey !== 'placeholder-key' &&
  !supabaseUrl.includes('your-project') &&
  !supabaseAnonKey.includes('your_supabase')

export const supabase = createClient(supabaseUrl, supabaseAnonKey)

export { isSupabaseConfigured }

// Database types
export interface Trade {
  id: string
  user_id: string
  status: 'Open' | 'Closed'
  entry_datetime: string
  exit_datetime: string | null
  ticker: string
  trade_type: 'option'
  // Option-specific fields
  option_type: 'Call' | 'Put'
  option_strike: number
  option_expiration: string
  contracts: number
  // Trade analysis fields
  trade_setup: string | null
  support_type: string | null
  price_to_vwap_position: 'Above' | 'At' | 'Below' | null
  // Common/derived fields
  mae_price: number | null
  mfe_price: number | null
  entry_price: number
  exit_price: number | null
  fees: number | null
  profit_loss: number | null
  percentage_return: number | null
  created_at: string
  updated_at: string
}

export interface DailyStats {
  date: string
  total_profit_loss: number
  total_r_return: number
  trade_count: number
  winning_trades: number
  losing_trades: number
}

export interface UserPreferences {
  id: string
  user_id: string
  theme: 'light' | 'dark'
  created_at: string
  updated_at: string
}

export interface TradeSetup {
  id: string
  user_id: string
  name: string
  created_at: string
  updated_at: string
}

export interface SupportType {
  id: string
  user_id: string
  name: string
  created_at: string
  updated_at: string
}
